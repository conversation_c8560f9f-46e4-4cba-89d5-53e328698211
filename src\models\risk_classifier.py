"""
Risk Clause Classification Models

Implements transformer-based models for risk clause detection and classification
in long financial documents. Supports Longformer, LED, and hierarchical approaches.
"""

import torch
import torch.nn as nn
from transformers import (
    AutoTokenizer, AutoModel, AutoConfig,
    LongformerTokenizer, LongformerModel,
    LEDTokenizer, LEDModel,
    TrainingArguments, Trainer
)
from typing import Dict, List, Tuple, Optional, Union
import numpy as np
from dataclasses import dataclass
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ModelConfig:
    """Configuration for risk classification models."""
    model_name: str = "microsoft/longformer-base-4096"
    max_length: int = 4096
    num_risk_categories: int = 12
    num_severity_levels: int = 4
    dropout_rate: float = 0.1
    learning_rate: float = 2e-5
    warmup_steps: int = 500
    weight_decay: float = 0.01
    use_hierarchical: bool = False
    chunk_size: int = 512
    chunk_overlap: int = 50


class RiskClassificationHead(nn.Module):
    """Multi-task classification head for risk detection."""
    
    def __init__(self, hidden_size: int, config: ModelConfig):
        super().__init__()
        self.config = config
        
        # Shared representation layer
        self.dropout = nn.Dropout(config.dropout_rate)
        self.dense = nn.Linear(hidden_size, hidden_size)
        self.activation = nn.GELU()
        
        # Task-specific heads
        self.risk_category_classifier = nn.Linear(hidden_size, config.num_risk_categories)
        self.severity_classifier = nn.Linear(hidden_size, config.num_severity_levels)
        self.binary_risk_classifier = nn.Linear(hidden_size, 2)  # Risk vs No-Risk
        
    def forward(self, hidden_states: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass for multi-task classification.
        
        Args:
            hidden_states: [batch_size, hidden_size] tensor
            
        Returns:
            Dictionary with logits for each task
        """
        # Shared representation
        x = self.dropout(hidden_states)
        x = self.dense(x)
        x = self.activation(x)
        x = self.dropout(x)
        
        # Task-specific predictions
        outputs = {
            'risk_category_logits': self.risk_category_classifier(x),
            'severity_logits': self.severity_classifier(x),
            'binary_risk_logits': self.binary_risk_classifier(x)
        }
        
        return outputs


class LongDocumentRiskClassifier(nn.Module):
    """
    Long-document risk classifier using Longformer or LED.
    Supports both span-level and document-level classification.
    """
    
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config
        
        # Load base model
        if "longformer" in config.model_name.lower():
            self.tokenizer = LongformerTokenizer.from_pretrained(config.model_name)
            self.encoder = LongformerModel.from_pretrained(config.model_name)
        elif "led" in config.model_name.lower():
            self.tokenizer = LEDTokenizer.from_pretrained(config.model_name)
            self.encoder = LEDModel.from_pretrained(config.model_name)
        else:
            # Fallback to AutoModel
            self.tokenizer = AutoTokenizer.from_pretrained(config.model_name)
            self.encoder = AutoModel.from_pretrained(config.model_name)
        
        # Add special tokens if needed
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Classification head
        hidden_size = self.encoder.config.hidden_size
        self.classification_head = RiskClassificationHead(hidden_size, config)
        
        # Span extraction head (for evidence spans)
        self.span_start_classifier = nn.Linear(hidden_size, 1)
        self.span_end_classifier = nn.Linear(hidden_size, 1)
        
    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: torch.Tensor,
        global_attention_mask: Optional[torch.Tensor] = None,
        start_positions: Optional[torch.Tensor] = None,
        end_positions: Optional[torch.Tensor] = None,
        risk_labels: Optional[torch.Tensor] = None,
        severity_labels: Optional[torch.Tensor] = None,
        binary_labels: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass for risk classification and span extraction.
        
        Args:
            input_ids: Token IDs [batch_size, seq_len]
            attention_mask: Attention mask [batch_size, seq_len]
            global_attention_mask: Global attention for Longformer [batch_size, seq_len]
            start_positions: Span start positions [batch_size]
            end_positions: Span end positions [batch_size]
            risk_labels: Risk category labels [batch_size]
            severity_labels: Severity labels [batch_size]
            binary_labels: Binary risk labels [batch_size]
            
        Returns:
            Dictionary with model outputs and losses
        """
        # Encoder forward pass
        encoder_kwargs = {
            'input_ids': input_ids,
            'attention_mask': attention_mask
        }
        
        # Add global attention for Longformer
        if global_attention_mask is not None and "longformer" in self.config.model_name.lower():
            encoder_kwargs['global_attention_mask'] = global_attention_mask
        
        encoder_outputs = self.encoder(**encoder_kwargs)
        sequence_output = encoder_outputs.last_hidden_state  # [batch_size, seq_len, hidden_size]
        
        # Document-level representation (CLS token or mean pooling)
        if hasattr(self.encoder.config, 'pooler_type'):
            # Use CLS token
            doc_representation = sequence_output[:, 0, :]  # [batch_size, hidden_size]
        else:
            # Mean pooling over non-padded tokens
            mask_expanded = attention_mask.unsqueeze(-1).expand(sequence_output.size()).float()
            sum_embeddings = torch.sum(sequence_output * mask_expanded, 1)
            sum_mask = torch.clamp(mask_expanded.sum(1), min=1e-9)
            doc_representation = sum_embeddings / sum_mask
        
        # Classification predictions
        classification_outputs = self.classification_head(doc_representation)
        
        # Span extraction predictions
        span_start_logits = self.span_start_classifier(sequence_output).squeeze(-1)  # [batch_size, seq_len]
        span_end_logits = self.span_end_classifier(sequence_output).squeeze(-1)    # [batch_size, seq_len]
        
        outputs = {
            'risk_category_logits': classification_outputs['risk_category_logits'],
            'severity_logits': classification_outputs['severity_logits'],
            'binary_risk_logits': classification_outputs['binary_risk_logits'],
            'span_start_logits': span_start_logits,
            'span_end_logits': span_end_logits,
            'sequence_output': sequence_output,
            'doc_representation': doc_representation
        }
        
        # Calculate losses if labels provided
        total_loss = 0
        if risk_labels is not None:
            loss_fct = nn.CrossEntropyLoss()
            risk_loss = loss_fct(classification_outputs['risk_category_logits'], risk_labels)
            outputs['risk_category_loss'] = risk_loss
            total_loss += risk_loss
        
        if severity_labels is not None:
            loss_fct = nn.CrossEntropyLoss()
            severity_loss = loss_fct(classification_outputs['severity_logits'], severity_labels)
            outputs['severity_loss'] = severity_loss
            total_loss += severity_loss
        
        if binary_labels is not None:
            loss_fct = nn.CrossEntropyLoss()
            binary_loss = loss_fct(classification_outputs['binary_risk_logits'], binary_labels)
            outputs['binary_risk_loss'] = binary_loss
            total_loss += binary_loss
        
        if start_positions is not None and end_positions is not None:
            # Span extraction loss
            loss_fct = nn.CrossEntropyLoss(ignore_index=-1)
            start_loss = loss_fct(span_start_logits, start_positions)
            end_loss = loss_fct(span_end_logits, end_positions)
            span_loss = (start_loss + end_loss) / 2
            outputs['span_loss'] = span_loss
            total_loss += span_loss
        
        if total_loss > 0:
            outputs['loss'] = total_loss
        
        return outputs
    
    def predict_risk(
        self,
        text: str,
        return_spans: bool = True,
        confidence_threshold: float = 0.5
    ) -> Dict:
        """
        Predict risk for a given text.
        
        Args:
            text: Input text to analyze
            return_spans: Whether to extract evidence spans
            confidence_threshold: Minimum confidence for predictions
            
        Returns:
            Dictionary with risk predictions and evidence spans
        """
        self.eval()
        
        # Tokenize input
        encoding = self.tokenizer(
            text,
            max_length=self.config.max_length,
            padding=True,
            truncation=True,
            return_tensors="pt"
        )
        
        # Set global attention for first token (CLS) if using Longformer
        global_attention_mask = None
        if "longformer" in self.config.model_name.lower():
            global_attention_mask = torch.zeros_like(encoding['attention_mask'])
            global_attention_mask[:, 0] = 1  # Global attention on CLS token
        
        with torch.no_grad():
            outputs = self.forward(
                input_ids=encoding['input_ids'],
                attention_mask=encoding['attention_mask'],
                global_attention_mask=global_attention_mask
            )
        
        # Process predictions
        risk_probs = torch.softmax(outputs['risk_category_logits'], dim=-1)
        severity_probs = torch.softmax(outputs['severity_logits'], dim=-1)
        binary_probs = torch.softmax(outputs['binary_risk_logits'], dim=-1)
        
        # Get top predictions
        risk_pred = torch.argmax(risk_probs, dim=-1).item()
        severity_pred = torch.argmax(severity_probs, dim=-1).item()
        binary_pred = torch.argmax(binary_probs, dim=-1).item()
        
        results = {
            'risk_category': risk_pred,
            'risk_category_confidence': risk_probs[0, risk_pred].item(),
            'severity': severity_pred,
            'severity_confidence': severity_probs[0, severity_pred].item(),
            'is_risk': binary_pred == 1,
            'risk_confidence': binary_probs[0, binary_pred].item(),
            'all_risk_probabilities': risk_probs[0].tolist(),
            'all_severity_probabilities': severity_probs[0].tolist()
        }
        
        # Extract evidence spans if requested
        if return_spans and results['is_risk'] and results['risk_confidence'] > confidence_threshold:
            span_start_probs = torch.softmax(outputs['span_start_logits'], dim=-1)
            span_end_probs = torch.softmax(outputs['span_end_logits'], dim=-1)
            
            # Find best span
            start_idx = torch.argmax(span_start_probs, dim=-1).item()
            end_idx = torch.argmax(span_end_probs, dim=-1).item()
            
            if start_idx <= end_idx:
                # Convert token indices to character spans
                tokens = self.tokenizer.convert_ids_to_tokens(encoding['input_ids'][0])
                span_tokens = tokens[start_idx:end_idx+1]
                span_text = self.tokenizer.convert_tokens_to_string(span_tokens)
                
                results['evidence_span'] = {
                    'text': span_text,
                    'start_token': start_idx,
                    'end_token': end_idx,
                    'confidence': (span_start_probs[0, start_idx] * span_end_probs[0, end_idx]).item()
                }
        
        return results


def create_model(config: ModelConfig) -> LongDocumentRiskClassifier:
    """Factory function to create risk classification model."""
    return LongDocumentRiskClassifier(config)


def load_pretrained_model(model_path: str, config: ModelConfig) -> LongDocumentRiskClassifier:
    """Load a pretrained risk classification model."""
    model = create_model(config)
    model.load_state_dict(torch.load(model_path, map_location='cpu'))
    return model
