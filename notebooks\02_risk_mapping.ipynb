{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Risk Taxonomy Mapping\n", "\n", "This notebook creates a comprehensive mapping from CUAD clause types to financial risk categories.\n", "\n", "## Objectives\n", "1. Load CUAD clause types from data exploration\n", "2. Apply risk taxonomy mapping rules\n", "3. Analyze risk category distribution\n", "4. Create training labels for risk detection models\n", "5. Export risk taxonomy for model training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup\n", "import sys\n", "sys.path.append('..')\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import json\n", "\n", "# Custom modules\n", "from src.data.cuad_loader import CUADLoader\n", "from src.data.risk_taxonomy import RiskTaxonomyMapper, RiskCategory, RiskSeverity\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"Set2\")\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load CUAD Clause Types"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load CUAD data and extract clause types\n", "loader = CUADLoader(\"../data/raw/\")\n", "mapper = RiskTaxonomyMapper()\n", "\n", "try:\n", "    clause_types = loader.extract_clause_types()\n", "    print(f\"✅ Loaded {len(clause_types)} unique clause types from CUAD\")\n", "    \n", "    # Show first 10 clause types\n", "    print(\"\\nFirst 10 clause types:\")\n", "    for i, clause in enumerate(clause_types[:10]):\n", "        print(f\"  {i+1}. {clause}\")\n", "        \nexcept Exception as e:\n", "    print(f\"❌ Error loading clause types: {e}\")\n", "    print(\"Using sample clause types for demonstration\")\n", "    clause_types = [\n", "        \"Governing Law\", \"Termination\", \"Cap on Liability\", \"IP Ownership Assignment\",\n", "        \"Force Majeure\", \"Compliance with Laws\", \"Non-Compete\", \"Insurance\",\n", "        \"Warranty Duration\", \"Liquidated Damages\", \"Change of Control\",\n", "        \"Indemnification\", \"Confidentiality\", \"Most Favored Nation\"\n", "    ]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Apply Risk Taxonomy Mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create risk taxonomy mappings\n", "mappings_df = mapper.create_risk_taxonomy_dataset(clause_types)\n", "\n", "print(f\"Risk Taxonomy Mapping Results:\")\n", "print(f\"Total clause types: {len(mappings_df)}\")\n", "print(f\"Unique risk categories: {mappings_df['risk_category'].nunique()}\")\n", "\n", "# Show mapping method distribution\n", "print(\"\\nMapping method distribution:\")\n", "method_counts = mappings_df['mapping_method'].value_counts()\n", "for method, count in method_counts.items():\n", "    print(f\"  {method}: {count} ({count/len(mappings_df)*100:.1f}%)\")\n", "\n", "# Display first 15 mappings\n", "print(\"\\nFirst 15 risk mappings:\")\n", "display_cols = ['clause_type', 'risk_category', 'risk_severity', 'mapping_method']\n", "print(mappings_df[display_cols].head(15).to_string(index=False))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Risk Category Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze risk category distribution\n", "risk_dist = mapper.get_risk_category_distribution(mappings_df)\n", "\n", "print(\"Risk Category Distribution:\")\n", "risk_df = pd.DataFrame([\n", "    {'risk_category': k, 'count': v, 'percentage': v/len(mappings_df)*100}\n", "    for k, v in risk_dist.items()\n", "]).sort_values('count', ascending=False)\n", "\n", "print(risk_df.to_string(index=False))\n", "\n", "# Plot risk category distribution\n", "plt.figure(figsize=(12, 8))\n", "plt.subplot(2, 1, 1)\n", "plt.bar(risk_df['risk_category'], risk_df['count'])\n", "plt.xticks(rotation=45, ha='right')\n", "plt.ylabel('Number of Clause Types')\n", "plt.title('Risk Category Distribution')\n", "\n", "plt.subplot(2, 1, 2)\n", "plt.pie(risk_df['count'], labels=risk_df['risk_category'], autopct='%1.1f%%')\n", "plt.title('Risk Category Proportions')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Risk Severity Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze risk severity distribution\n", "severity_dist = mappings_df['risk_severity'].value_counts().sort_index()\n", "\n", "print(\"Risk Severity Distribution:\")\n", "severity_labels = {1: 'Low', 2: 'Medium', 3: 'High', 4: 'Critical'}\n", "for severity, count in severity_dist.items():\n", "    label = severity_labels.get(severity, f'Level {severity}')\n", "    print(f\"  {label}: {count} ({count/len(mappings_df)*100:.1f}%)\")\n", "\n", "# Plot severity distribution\n", "plt.figure(figsize=(10, 6))\n", "labels = [severity_labels.get(s, f'Level {s}') for s in severity_dist.index]\n", "plt.bar(labels, severity_dist.values, color=['green', 'yellow', 'orange', 'red'])\n", "plt.ylabel('Number of Clause Types')\n", "plt.title('Risk Severity Distribution')\n", "plt.show()\n", "\n", "# Cross-tabulation of category vs severity\n", "print(\"\\nRisk Category vs Severity Cross-tabulation:\")\n", "crosstab = pd.crosstab(mappings_df['risk_category'], mappings_df['risk_severity'])\n", "crosstab.columns = [severity_labels.get(col, f'Level {col}') for col in crosstab.columns]\n", "print(crosstab)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. High-Risk Clause Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identify high-risk and critical clauses\n", "high_risk_clauses = mappings_df[mappings_df['risk_severity'] >= 3]\n", "\n", "print(f\"High-Risk and Critical Clauses ({len(high_risk_clauses)} total):\")\n", "print(\"=\" * 60)\n", "\n", "for _, row in high_risk_clauses.iterrows():\n", "    severity_label = severity_labels.get(row['risk_severity'], f\"Level {row['risk_severity']}\")\n", "    print(f\"\\n{row['clause_type']}\")\n", "    print(f\"  Category: {row['risk_category']}\")\n", "    print(f\"  Severity: {severity_label}\")\n", "    print(f\"  Description: {row['description']}\")\n", "    print(f\"  Mapping: {row['mapping_method']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Mapping Quality Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assess mapping quality\n", "print(\"Mapping Quality Assessment:\")\n", "print(\"=\" * 40)\n", "\n", "# Direct mappings (highest confidence)\n", "direct_mappings = mappings_df[mappings_df['mapping_method'] == 'direct']\n", "print(f\"Direct mappings: {len(direct_mappings)} ({len(direct_mappings)/len(mappings_df)*100:.1f}%)\")\n", "\n", "# Keyword mappings (medium confidence)\n", "keyword_mappings = mappings_df[mappings_df['mapping_method'] == 'keyword']\n", "print(f\"Keyword mappings: {len(keyword_mappings)} ({len(keyword_mappings)/len(mappings_df)*100:.1f}%)\")\n", "\n", "# Default mappings (need manual review)\n", "default_mappings = mappings_df[mappings_df['mapping_method'] == 'default']\n", "print(f\"Default mappings: {len(default_mappings)} ({len(default_mappings)/len(mappings_df)*100:.1f}%)\")\n", "\n", "if len(default_mappings) > 0:\n", "    print(\"\\nClauses needing manual review (default mappings):\")\n", "    for clause in default_mappings['clause_type'].head(10):\n", "        print(f\"  - {clause}\")\n", "    if len(default_mappings) > 10:\n", "        print(f\"  ... and {len(default_mappings) - 10} more\")\n", "\n", "# Show keyword mapping quality\n", "if len(keyword_mappings) > 0:\n", "    print(\"\\nKeyword mapping examples:\")\n", "    for _, row in keyword_mappings.head(5).iterrows():\n", "        matches = row.get('keyword_matches', 'N/A')\n", "        print(f\"  {row['clause_type']} → {row['risk_category']} (matches: {matches})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Create Training Labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create training labels for model development\n", "training_labels = mappings_df.copy()\n", "\n", "# Add binary risk indicators\n", "training_labels['is_high_risk'] = (training_labels['risk_severity'] >= 3).astype(int)\n", "training_labels['is_financial_risk'] = training_labels['risk_category'].isin([\n", "    'Credit Risk', 'Market Risk', 'Liquidity Risk'\n", "]).astype(int)\n", "training_labels['is_operational_risk'] = training_labels['risk_category'].isin([\n", "    'Operational Risk', 'Supply Chain Risk', 'Technology Risk'\n", "]).astype(int)\n", "\n", "# Create risk category encoding\n", "risk_categories = sorted(training_labels['risk_category'].unique())\n", "category_encoding = {cat: idx for idx, cat in enumerate(risk_categories)}\n", "training_labels['risk_category_id'] = training_labels['risk_category'].map(category_encoding)\n", "\n", "print(\"Training Labels Created:\")\n", "print(f\"Total samples: {len(training_labels)}\")\n", "print(f\"High-risk samples: {training_labels['is_high_risk'].sum()}\")\n", "print(f\"Financial risk samples: {training_labels['is_financial_risk'].sum()}\")\n", "print(f\"Operational risk samples: {training_labels['is_operational_risk'].sum()}\")\n", "\n", "print(\"\\nRisk category encoding:\")\n", "for category, idx in category_encoding.items():\n", "    print(f\"  {idx}: {category}\")\n", "\n", "# Show sample training data\n", "print(\"\\nSample training labels:\")\n", "sample_cols = ['clause_type', 'risk_category_id', 'risk_severity', 'is_high_risk']\n", "print(training_labels[sample_cols].head(10).to_string(index=False))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Export Risk Taxonomy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export risk taxonomy and training labels\n", "stats = mapper.export_taxonomy(mappings_df, \"../data/risk_taxonomy/\")\n", "\n", "# Export training labels\n", "output_dir = Path(\"../data/risk_taxonomy/\")\n", "training_labels.to_csv(output_dir / \"training_labels.csv\", index=False)\n", "\n", "# Export category encoding\n", "with open(output_dir / \"category_encoding.json\", 'w') as f:\n", "    json.dump(category_encoding, f, indent=2)\n", "\n", "print(\"✅ Risk taxonomy exported successfully!\")\n", "print(f\"\\nExported files:\")\n", "for file in output_dir.glob(\"*\"):\n", "    print(f\"  {file.name}\")\n", "\n", "print(f\"\\nTaxonomy Statistics:\")\n", "for key, value in stats.items():\n", "    print(f\"  {key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Validation and Quality Checks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform validation checks\n", "print(\"Risk Taxonomy Validation:\")\n", "print(\"=\" * 30)\n", "\n", "# Check for missing mappings\n", "unmapped = mappings_df[mappings_df['risk_category'] == 'Other Risk']\n", "print(f\"✓ Unmapped clauses: {len(unmapped)} ({len(unmapped)/len(mappings_df)*100:.1f}%)\")\n", "\n", "# Check severity distribution\n", "severity_balance = mappings_df['risk_severity'].value_counts(normalize=True)\n", "print(f\"✓ Severity distribution balanced: {severity_balance.std():.3f} (lower is better)\")\n", "\n", "# Check category coverage\n", "categories_used = len(mappings_df['risk_category'].unique())\n", "total_categories = len([cat for cat in RiskCategory])\n", "print(f\"✓ Risk category coverage: {categories_used}/{total_categories} ({categories_used/total_categories*100:.1f}%)\")\n", "\n", "# Check for potential issues\n", "issues = []\n", "if len(unmapped) > len(mappings_df) * 0.3:  # >30% unmapped\n", "    issues.append(\"High percentage of unmapped clauses\")\n", "if severity_balance.std() > 0.3:  # Unbalanced severity\n", "    issues.append(\"Unbalanced severity distribution\")\n", "if categories_used < total_categories * 0.5:  # <50% category coverage\n", "    issues.append(\"Low risk category coverage\")\n", "\n", "if issues:\n", "    print(\"\\n⚠️  Potential issues:\")\n", "    for issue in issues:\n", "        print(f\"  - {issue}\")\nelse:\n", "    print(\"\\n✅ No major issues detected\")\n", "\n", "print(\"\\n📊 Ready for model training!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook created a comprehensive risk taxonomy mapping that:\n", "\n", "1. **Maps CUAD clause types** to 12 standard financial risk categories\n", "2. **Assigns risk severity levels** (Low, Medium, High, Critical)\n", "3. **Provides training labels** for supervised learning\n", "4. **Exports structured data** for model development\n", "\n", "### Key Outputs:\n", "- `cuad_risk_mapping.csv`: Complete clause-to-risk mappings\n", "- `training_labels.csv`: ML-ready training labels\n", "- `category_encoding.json`: Risk category encodings\n", "- Individual risk category files for analysis\n", "\n", "### Next Steps:\n", "1. Review unmapped clauses and improve keyword mappings\n", "2. Use training labels for model development\n", "3. Implement risk detection pipeline with long-document transformers\n", "4. Evaluate model performance on risk classification tasks"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}