"""
Dataset classes for risk clause detection training.

Handles CUAD data preprocessing, tokenization, and batch creation for
long-document transformer training.
"""

import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import json
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class RiskClauseDataset(Dataset):
    """Dataset for risk clause detection and classification."""
    
    def __init__(
        self,
        data: List[Dict],
        tokenizer: AutoTokenizer,
        max_length: int = 4096,
        risk_taxonomy: Optional[Dict] = None,
        include_spans: bool = True,
        chunk_long_docs: bool = True,
        chunk_size: int = 512,
        chunk_overlap: int = 50
    ):
        """
        Initialize risk clause dataset.
        
        Args:
            data: List of processed CUAD documents
            tokenizer: Tokenizer for text encoding
            max_length: Maximum sequence length
            risk_taxonomy: Risk category mapping
            include_spans: Whether to include span extraction labels
            chunk_long_docs: Whether to chunk documents exceeding max_length
            chunk_size: Size of chunks for long documents
            chunk_overlap: Overlap between chunks
        """
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.risk_taxonomy = risk_taxonomy or {}
        self.include_spans = include_spans
        self.chunk_long_docs = chunk_long_docs
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        
        # Process data into training examples
        self.examples = self._process_data(data)
        
        logger.info(f"Created dataset with {len(self.examples)} examples")
    
    def _process_data(self, data: List[Dict]) -> List[Dict]:
        """Process raw CUAD data into training examples."""
        examples = []
        
        for doc in data:
            doc_text = doc.get('full_text', '')
            if not doc_text:
                continue
            
            # Process each clause in the document
            for clause in doc.get('clauses', []):
                clause_type = clause.get('clause_type', '')
                span_text = clause.get('span_text', '')
                span_start = clause.get('span_start', 0)
                span_end = clause.get('span_end', 0)
                
                if not span_text:
                    continue
                
                # Get risk mapping
                risk_info = self.risk_taxonomy.get(clause_type, {})
                risk_category = risk_info.get('risk_category_id', 0)
                risk_severity = risk_info.get('risk_severity', 1) - 1  # Convert to 0-indexed
                is_risk = risk_info.get('is_high_risk', 0)
                
                # Create training example
                example = {
                    'text': doc_text,
                    'clause_type': clause_type,
                    'span_text': span_text,
                    'span_start': span_start,
                    'span_end': span_end,
                    'risk_category': risk_category,
                    'risk_severity': risk_severity,
                    'is_risk': is_risk,
                    'doc_title': doc.get('title', ''),
                    'doc_length': len(doc_text)
                }
                
                examples.append(example)
        
        # Handle long documents by chunking if needed
        if self.chunk_long_docs:
            examples = self._chunk_long_examples(examples)
        
        return examples
    
    def _chunk_long_examples(self, examples: List[Dict]) -> List[Dict]:
        """Chunk examples that exceed max_length."""
        chunked_examples = []
        
        for example in examples:
            text = example['text']
            
            # Quick tokenization to check length
            tokens = self.tokenizer.tokenize(text)
            
            if len(tokens) <= self.max_length - 2:  # Account for special tokens
                chunked_examples.append(example)
            else:
                # Create chunks
                chunks = self._create_text_chunks(text, tokens)
                
                for i, (chunk_text, chunk_tokens) in enumerate(chunks):
                    # Adjust span positions for chunk
                    chunk_example = example.copy()
                    chunk_example['text'] = chunk_text
                    chunk_example['chunk_id'] = i
                    chunk_example['total_chunks'] = len(chunks)
                    
                    # Check if span falls within this chunk
                    span_start = example['span_start']
                    span_end = example['span_end']
                    chunk_start = sum(len(self.tokenizer.tokenize(chunks[j][0])) for j in range(i))
                    chunk_end = chunk_start + len(chunk_tokens)
                    
                    if span_start >= chunk_start and span_end <= chunk_end:
                        # Span is in this chunk, adjust positions
                        chunk_example['span_start'] = span_start - chunk_start
                        chunk_example['span_end'] = span_end - chunk_start
                        chunk_example['has_span'] = True
                    else:
                        # Span not in this chunk
                        chunk_example['span_start'] = -1
                        chunk_example['span_end'] = -1
                        chunk_example['has_span'] = False
                    
                    chunked_examples.append(chunk_example)
        
        return chunked_examples
    
    def _create_text_chunks(self, text: str, tokens: List[str]) -> List[Tuple[str, List[str]]]:
        """Create overlapping chunks from long text."""
        chunks = []
        start_idx = 0
        
        while start_idx < len(tokens):
            end_idx = min(start_idx + self.chunk_size, len(tokens))
            chunk_tokens = tokens[start_idx:end_idx]
            chunk_text = self.tokenizer.convert_tokens_to_string(chunk_tokens)
            
            chunks.append((chunk_text, chunk_tokens))
            
            if end_idx >= len(tokens):
                break
            
            # Move start with overlap
            start_idx = end_idx - self.chunk_overlap
        
        return chunks
    
    def __len__(self) -> int:
        return len(self.examples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a single training example."""
        example = self.examples[idx]
        
        # Tokenize text
        encoding = self.tokenizer(
            example['text'],
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        # Prepare output
        item = {
            'input_ids': encoding['input_ids'].squeeze(0),
            'attention_mask': encoding['attention_mask'].squeeze(0),
            'risk_category': torch.tensor(example['risk_category'], dtype=torch.long),
            'risk_severity': torch.tensor(example['risk_severity'], dtype=torch.long),
            'is_risk': torch.tensor(example['is_risk'], dtype=torch.long)
        }
        
        # Add global attention mask for Longformer
        if 'longformer' in self.tokenizer.name_or_path.lower():
            global_attention_mask = torch.zeros_like(item['attention_mask'])
            global_attention_mask[0] = 1  # Global attention on CLS token
            item['global_attention_mask'] = global_attention_mask
        
        # Add span labels if available and requested
        if self.include_spans and example.get('has_span', True):
            span_start = example.get('span_start', -1)
            span_end = example.get('span_end', -1)
            
            # Convert character positions to token positions
            if span_start >= 0 and span_end >= 0:
                # Find token positions for character spans
                token_start, token_end = self._char_to_token_positions(
                    example['text'], span_start, span_end, encoding
                )
                item['start_positions'] = torch.tensor(token_start, dtype=torch.long)
                item['end_positions'] = torch.tensor(token_end, dtype=torch.long)
            else:
                item['start_positions'] = torch.tensor(-1, dtype=torch.long)
                item['end_positions'] = torch.tensor(-1, dtype=torch.long)
        
        return item
    
    def _char_to_token_positions(
        self, 
        text: str, 
        char_start: int, 
        char_end: int, 
        encoding
    ) -> Tuple[int, int]:
        """Convert character positions to token positions."""
        try:
            # Use tokenizer's char_to_token method if available
            token_start = encoding.char_to_token(char_start)
            token_end = encoding.char_to_token(char_end - 1)  # End is exclusive
            
            if token_start is None:
                token_start = 0
            if token_end is None:
                token_end = len(encoding['input_ids'][0]) - 1
            
            return token_start, token_end
        except:
            # Fallback: approximate token positions
            tokens = self.tokenizer.tokenize(text)
            approx_chars_per_token = len(text) / len(tokens) if tokens else 1
            
            token_start = int(char_start / approx_chars_per_token)
            token_end = int(char_end / approx_chars_per_token)
            
            # Clamp to valid range
            token_start = max(0, min(token_start, len(tokens) - 1))
            token_end = max(token_start, min(token_end, len(tokens) - 1))
            
            return token_start, token_end


class RiskDataModule:
    """Data module for risk clause detection training."""
    
    def __init__(
        self,
        train_data: List[Dict],
        val_data: List[Dict],
        test_data: Optional[List[Dict]],
        tokenizer: AutoTokenizer,
        risk_taxonomy: Dict,
        batch_size: int = 8,
        max_length: int = 4096,
        num_workers: int = 4
    ):
        """
        Initialize data module.
        
        Args:
            train_data: Training documents
            val_data: Validation documents  
            test_data: Test documents (optional)
            tokenizer: Tokenizer for text encoding
            risk_taxonomy: Risk category mapping
            batch_size: Batch size for training
            max_length: Maximum sequence length
            num_workers: Number of data loading workers
        """
        self.train_data = train_data
        self.val_data = val_data
        self.test_data = test_data
        self.tokenizer = tokenizer
        self.risk_taxonomy = risk_taxonomy
        self.batch_size = batch_size
        self.max_length = max_length
        self.num_workers = num_workers
        
        # Create datasets
        self.train_dataset = RiskClauseDataset(
            train_data, tokenizer, max_length, risk_taxonomy
        )
        self.val_dataset = RiskClauseDataset(
            val_data, tokenizer, max_length, risk_taxonomy
        )
        if test_data:
            self.test_dataset = RiskClauseDataset(
                test_data, tokenizer, max_length, risk_taxonomy
            )
    
    def train_dataloader(self) -> DataLoader:
        """Create training data loader."""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=True
        )
    
    def val_dataloader(self) -> DataLoader:
        """Create validation data loader."""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True
        )
    
    def test_dataloader(self) -> Optional[DataLoader]:
        """Create test data loader."""
        if self.test_data:
            return DataLoader(
                self.test_dataset,
                batch_size=self.batch_size,
                shuffle=False,
                num_workers=self.num_workers,
                pin_memory=True
            )
        return None


def load_risk_taxonomy(taxonomy_path: str) -> Dict:
    """Load risk taxonomy mapping from file."""
    taxonomy_file = Path(taxonomy_path)
    
    if taxonomy_file.suffix == '.csv':
        df = pd.read_csv(taxonomy_file)
        taxonomy = {}
        for _, row in df.iterrows():
            taxonomy[row['clause_type']] = {
                'risk_category_id': row.get('risk_category_id', 0),
                'risk_severity': row.get('risk_severity', 1),
                'is_high_risk': row.get('is_high_risk', 0)
            }
        return taxonomy
    elif taxonomy_file.suffix == '.json':
        with open(taxonomy_file, 'r') as f:
            return json.load(f)
    else:
        raise ValueError(f"Unsupported taxonomy file format: {taxonomy_file.suffix}")


def create_data_splits(
    processed_docs: List[Dict], 
    train_ratio: float = 0.7,
    val_ratio: float = 0.15,
    test_ratio: float = 0.15,
    random_seed: int = 42
) -> Tuple[List[Dict], List[Dict], List[Dict]]:
    """Split processed documents into train/val/test sets."""
    np.random.seed(random_seed)
    
    # Shuffle documents
    docs = processed_docs.copy()
    np.random.shuffle(docs)
    
    # Calculate split indices
    n_docs = len(docs)
    train_end = int(n_docs * train_ratio)
    val_end = train_end + int(n_docs * val_ratio)
    
    # Create splits
    train_docs = docs[:train_end]
    val_docs = docs[train_end:val_end]
    test_docs = docs[val_end:]
    
    logger.info(f"Data splits: Train={len(train_docs)}, Val={len(val_docs)}, Test={len(test_docs)}")
    
    return train_docs, val_docs, test_docs
