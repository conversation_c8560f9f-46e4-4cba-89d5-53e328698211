"""
CUAD Dataset Loader and Processor

Handles loading and preprocessing of the CUAD v1 dataset for risk clause detection.
"""

import json
import pandas as pd
import os
from typing import Dict, List, Tuple, Optional
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CUADLoader:
    """Loader for CUAD v1 dataset with risk-focused preprocessing."""
    
    def __init__(self, data_dir: str = "data/raw/"):
        """
        Initialize CUAD loader.
        
        Args:
            data_dir: Path to directory containing CUAD v1 files
        """
        self.data_dir = Path(data_dir)
        self.annotations_file = self.data_dir / "CUAD_v1.json"
        self.master_clauses_file = self.data_dir / "master_clauses.csv"
        self.contracts_dir = self.data_dir / "full_contract_txt"
        self.label_reports_dir = self.data_dir / "label_group_xlsx"
        
    def load_annotations(self) -> Dict:
        """Load main CUAD annotations from JSON file."""
        if not self.annotations_file.exists():
            raise FileNotFoundError(f"CUAD annotations not found at {self.annotations_file}")
            
        logger.info(f"Loading CUAD annotations from {self.annotations_file}")
        with open(self.annotations_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"Loaded {len(data.get('data', []))} documents")
        return data
    
    def load_master_clauses(self) -> pd.DataFrame:
        """Load master clauses metadata."""
        if not self.master_clauses_file.exists():
            raise FileNotFoundError(f"Master clauses not found at {self.master_clauses_file}")
            
        logger.info(f"Loading master clauses from {self.master_clauses_file}")
        return pd.read_csv(self.master_clauses_file)
    
    def get_contract_text(self, filename: str) -> str:
        """Load full contract text by filename."""
        # Check both Part_I and Part_II directories
        for part_dir in ["Part_I", "Part_II"]:
            contract_path = self.contracts_dir / part_dir / filename
            if contract_path.exists():
                with open(contract_path, 'r', encoding='utf-8') as f:
                    return f.read()
        
        raise FileNotFoundError(f"Contract text not found: {filename}")
    
    def extract_clause_types(self) -> List[str]:
        """Extract all unique clause types from annotations."""
        data = self.load_annotations()
        clause_types = set()
        
        for document in data.get('data', []):
            for paragraph in document.get('paragraphs', []):
                for qa in paragraph.get('qas', []):
                    clause_types.add(qa.get('question', '').strip())
        
        return sorted(list(clause_types))
    
    def process_document(self, doc_data: Dict) -> Dict:
        """
        Process a single document from CUAD annotations.
        
        Args:
            doc_data: Document data from CUAD JSON
            
        Returns:
            Processed document with extracted spans and metadata
        """
        title = doc_data.get('title', '')
        
        processed_doc = {
            'title': title,
            'filename': title,  # In CUAD, title is the filename
            'clauses': [],
            'text_length': 0,
            'num_clauses': 0
        }
        
        # Load full contract text
        try:
            full_text = self.get_contract_text(title)
            processed_doc['full_text'] = full_text
            processed_doc['text_length'] = len(full_text)
        except FileNotFoundError:
            logger.warning(f"Could not load full text for {title}")
            processed_doc['full_text'] = ""
        
        # Process paragraphs and extract clause spans
        for paragraph in doc_data.get('paragraphs', []):
            context = paragraph.get('context', '')
            
            for qa in paragraph.get('qas', []):
                clause_type = qa.get('question', '').strip()
                
                # Process answers (spans)
                for answer in qa.get('answers', []):
                    span_text = answer.get('text', '')
                    span_start = answer.get('answer_start', 0)
                    
                    if span_text.strip():  # Only include non-empty spans
                        clause_info = {
                            'clause_type': clause_type,
                            'span_text': span_text,
                            'span_start': span_start,
                            'span_end': span_start + len(span_text),
                            'context': context,
                            'context_start': paragraph.get('context_start', 0) if 'context_start' in paragraph else None
                        }
                        processed_doc['clauses'].append(clause_info)
        
        processed_doc['num_clauses'] = len(processed_doc['clauses'])
        return processed_doc
    
    def load_processed_dataset(self) -> List[Dict]:
        """Load and process the entire CUAD dataset."""
        data = self.load_annotations()
        processed_docs = []
        
        logger.info("Processing CUAD documents...")
        for doc_data in data.get('data', []):
            try:
                processed_doc = self.process_document(doc_data)
                processed_docs.append(processed_doc)
            except Exception as e:
                logger.error(f"Error processing document {doc_data.get('title', 'unknown')}: {e}")
        
        logger.info(f"Successfully processed {len(processed_docs)} documents")
        return processed_docs
    
    def get_dataset_statistics(self) -> Dict:
        """Get comprehensive statistics about the CUAD dataset."""
        processed_docs = self.load_processed_dataset()
        clause_types = self.extract_clause_types()
        
        # Calculate statistics
        total_docs = len(processed_docs)
        total_clauses = sum(doc['num_clauses'] for doc in processed_docs)
        total_text_length = sum(doc['text_length'] for doc in processed_docs)
        
        # Clause type distribution
        clause_type_counts = {}
        for doc in processed_docs:
            for clause in doc['clauses']:
                clause_type = clause['clause_type']
                clause_type_counts[clause_type] = clause_type_counts.get(clause_type, 0) + 1
        
        stats = {
            'total_documents': total_docs,
            'total_clauses': total_clauses,
            'unique_clause_types': len(clause_types),
            'clause_types': clause_types,
            'clause_type_distribution': clause_type_counts,
            'avg_clauses_per_doc': total_clauses / total_docs if total_docs > 0 else 0,
            'avg_text_length': total_text_length / total_docs if total_docs > 0 else 0,
            'total_text_length': total_text_length
        }
        
        return stats


def main():
    """Example usage of CUADLoader."""
    loader = CUADLoader()
    
    # Get dataset statistics
    stats = loader.get_dataset_statistics()
    print("CUAD Dataset Statistics:")
    print(f"Total documents: {stats['total_documents']}")
    print(f"Total clauses: {stats['total_clauses']}")
    print(f"Unique clause types: {stats['unique_clause_types']}")
    print(f"Average clauses per document: {stats['avg_clauses_per_doc']:.2f}")
    print(f"Average text length: {stats['avg_text_length']:.0f} characters")
    
    # Show top clause types
    clause_dist = stats['clause_type_distribution']
    top_clauses = sorted(clause_dist.items(), key=lambda x: x[1], reverse=True)[:10]
    print("\nTop 10 clause types:")
    for clause_type, count in top_clauses:
        print(f"  {clause_type}: {count}")


if __name__ == "__main__":
    main()
