"""
CUAD Data Analysis and Exploration Tools

Provides comprehensive analysis of the CUAD dataset structure, clause distributions,
and text characteristics for risk detection pipeline development.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import json
from pathlib import Path
from collections import Counter, defaultdict
import re
from src.data.cuad_loader import CUADLoader

class CUADAnalyzer:
    """Comprehensive analyzer for CUAD dataset."""
    
    def __init__(self, data_dir: str = "data/raw/"):
        """Initialize analyzer with CUAD loader."""
        self.loader = CUADLoader(data_dir)
        self.processed_docs = None
        self.stats = None
        
    def load_data(self):
        """Load and cache processed data."""
        if self.processed_docs is None:
            self.processed_docs = self.loader.load_processed_dataset()
            self.stats = self.loader.get_dataset_statistics()
        return self.processed_docs
    
    def analyze_clause_distribution(self) -> pd.DataFrame:
        """Analyze distribution of clause types."""
        self.load_data()
        
        clause_dist = self.stats['clause_type_distribution']
        df = pd.DataFrame([
            {'clause_type': k, 'count': v, 'percentage': v/sum(clause_dist.values())*100}
            for k, v in clause_dist.items()
        ]).sort_values('count', ascending=False)
        
        return df
    
    def analyze_document_lengths(self) -> Dict:
        """Analyze document length characteristics."""
        self.load_data()
        
        lengths = [doc['text_length'] for doc in self.processed_docs]
        clause_counts = [doc['num_clauses'] for doc in self.processed_docs]
        
        return {
            'text_length_stats': {
                'mean': np.mean(lengths),
                'median': np.median(lengths),
                'std': np.std(lengths),
                'min': np.min(lengths),
                'max': np.max(lengths),
                'q25': np.percentile(lengths, 25),
                'q75': np.percentile(lengths, 75)
            },
            'clause_count_stats': {
                'mean': np.mean(clause_counts),
                'median': np.median(clause_counts),
                'std': np.std(clause_counts),
                'min': np.min(clause_counts),
                'max': np.max(clause_counts)
            },
            'raw_lengths': lengths,
            'raw_clause_counts': clause_counts
        }
    
    def analyze_span_characteristics(self) -> Dict:
        """Analyze characteristics of clause spans."""
        self.load_data()
        
        span_lengths = []
        span_positions = []
        
        for doc in self.processed_docs:
            doc_length = doc['text_length']
            for clause in doc['clauses']:
                span_len = len(clause['span_text'])
                span_lengths.append(span_len)
                
                # Relative position in document
                if doc_length > 0:
                    rel_pos = clause['span_start'] / doc_length
                    span_positions.append(rel_pos)
        
        return {
            'span_length_stats': {
                'mean': np.mean(span_lengths),
                'median': np.median(span_lengths),
                'std': np.std(span_lengths),
                'min': np.min(span_lengths),
                'max': np.max(span_lengths)
            },
            'span_position_stats': {
                'mean': np.mean(span_positions),
                'median': np.median(span_positions),
                'std': np.std(span_positions)
            },
            'raw_span_lengths': span_lengths,
            'raw_span_positions': span_positions
        }
    
    def identify_risk_related_clauses(self) -> List[str]:
        """Identify clause types that are potentially risk-related."""
        risk_keywords = [
            'liability', 'risk', 'indemnif', 'warranty', 'disclaim',
            'limitation', 'damage', 'loss', 'breach', 'default',
            'termination', 'penalty', 'force majeure', 'insurance',
            'compliance', 'regulatory', 'audit', 'confidential'
        ]
        
        clause_types = self.stats['clause_types'] if self.stats else self.loader.extract_clause_types()
        risk_related = []
        
        for clause_type in clause_types:
            clause_lower = clause_type.lower()
            if any(keyword in clause_lower for keyword in risk_keywords):
                risk_related.append(clause_type)
        
        return risk_related
    
    def analyze_contract_types(self) -> Dict:
        """Analyze distribution of contract types based on filenames."""
        self.load_data()
        
        contract_types = defaultdict(int)
        type_patterns = {
            'service': r'service|consulting|professional',
            'supply': r'supply|vendor|procurement',
            'distribution': r'distribut|reseller|dealer',
            'licensing': r'licens|ip|intellectual',
            'employment': r'employ|executive|compensation',
            'partnership': r'partner|joint|alliance',
            'merger': r'merger|acquisition|purchase',
            'lease': r'lease|rental|property',
            'loan': r'loan|credit|financing',
            'insurance': r'insurance|coverage|policy'
        }
        
        for doc in self.processed_docs:
            filename = doc['filename'].lower()
            doc_type = 'other'
            
            for type_name, pattern in type_patterns.items():
                if re.search(pattern, filename):
                    doc_type = type_name
                    break
            
            contract_types[doc_type] += 1
        
        return dict(contract_types)
    
    def generate_summary_report(self) -> str:
        """Generate comprehensive summary report."""
        self.load_data()
        
        # Get all analyses
        clause_dist = self.analyze_clause_distribution()
        doc_analysis = self.analyze_document_lengths()
        span_analysis = self.analyze_span_characteristics()
        risk_clauses = self.identify_risk_related_clauses()
        contract_types = self.analyze_contract_types()
        
        report = f"""
CUAD Dataset Analysis Report
{'='*50}

Dataset Overview:
- Total Documents: {self.stats['total_documents']:,}
- Total Clauses: {self.stats['total_clauses']:,}
- Unique Clause Types: {self.stats['unique_clause_types']}
- Average Clauses per Document: {self.stats['avg_clauses_per_doc']:.1f}

Document Characteristics:
- Average Length: {doc_analysis['text_length_stats']['mean']:,.0f} characters
- Median Length: {doc_analysis['text_length_stats']['median']:,.0f} characters
- Length Range: {doc_analysis['text_length_stats']['min']:,} - {doc_analysis['text_length_stats']['max']:,}

Clause Span Characteristics:
- Average Span Length: {span_analysis['span_length_stats']['mean']:.0f} characters
- Median Span Length: {span_analysis['span_length_stats']['median']:.0f} characters
- Span Length Range: {span_analysis['span_length_stats']['min']} - {span_analysis['span_length_stats']['max']}

Top 10 Most Common Clause Types:
"""
        
        for i, row in clause_dist.head(10).iterrows():
            report += f"  {row['clause_type']}: {row['count']} ({row['percentage']:.1f}%)\n"
        
        report += f"\nRisk-Related Clause Types ({len(risk_clauses)} found):\n"
        for clause in risk_clauses[:10]:  # Show first 10
            count = self.stats['clause_type_distribution'].get(clause, 0)
            report += f"  {clause}: {count}\n"
        
        report += f"\nContract Type Distribution:\n"
        for contract_type, count in sorted(contract_types.items(), key=lambda x: x[1], reverse=True):
            report += f"  {contract_type.title()}: {count}\n"
        
        return report
    
    def plot_clause_distribution(self, top_n: int = 20, figsize: Tuple[int, int] = (12, 8)):
        """Plot clause type distribution."""
        clause_dist = self.analyze_clause_distribution()
        
        plt.figure(figsize=figsize)
        top_clauses = clause_dist.head(top_n)
        
        plt.barh(range(len(top_clauses)), top_clauses['count'])
        plt.yticks(range(len(top_clauses)), top_clauses['clause_type'])
        plt.xlabel('Number of Occurrences')
        plt.title(f'Top {top_n} Clause Types in CUAD Dataset')
        plt.gca().invert_yaxis()
        plt.tight_layout()
        return plt.gcf()
    
    def plot_document_lengths(self, figsize: Tuple[int, int] = (12, 6)):
        """Plot document length distribution."""
        doc_analysis = self.analyze_document_lengths()
        lengths = doc_analysis['raw_lengths']
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)
        
        # Histogram
        ax1.hist(lengths, bins=50, alpha=0.7, edgecolor='black')
        ax1.set_xlabel('Document Length (characters)')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Document Length Distribution')
        
        # Box plot
        ax2.boxplot(lengths)
        ax2.set_ylabel('Document Length (characters)')
        ax2.set_title('Document Length Box Plot')
        
        plt.tight_layout()
        return fig
    
    def export_analysis_results(self, output_dir: str = "data/processed/"):
        """Export analysis results to files."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Export clause distribution
        clause_dist = self.analyze_clause_distribution()
        clause_dist.to_csv(output_path / "clause_distribution.csv", index=False)
        
        # Export summary report
        report = self.generate_summary_report()
        with open(output_path / "analysis_report.txt", 'w') as f:
            f.write(report)
        
        # Export risk-related clauses
        risk_clauses = self.identify_risk_related_clauses()
        with open(output_path / "risk_related_clauses.json", 'w') as f:
            json.dump(risk_clauses, f, indent=2)
        
        print(f"Analysis results exported to {output_path}")


def main():
    """Example usage of CUADAnalyzer."""
    analyzer = CUADAnalyzer()
    
    # Generate and print summary report
    report = analyzer.generate_summary_report()
    print(report)
    
    # Export results
    analyzer.export_analysis_results()


if __name__ == "__main__":
    main()
