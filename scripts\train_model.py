#!/usr/bin/env python3
"""
Training script for risk clause detection models.

This script provides a command-line interface for training models
with different configurations. Designed for Colab/Kaggle execution.

Usage:
    python scripts/train_model.py --config longformer_base --data_dir data/raw/
    python scripts/train_model.py --config legal_bert --use_wandb --epochs 5
"""

import argparse
import yaml
import logging
import sys
from pathlib import Path
import torch
import json

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data.cuad_loader import CUADLoader
from src.training.dataset import RiskDataModule, create_data_splits, load_risk_taxonomy
from src.models.risk_classifier import ModelConfig, create_model
from src.training.trainer import create_trainer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_config(config_name: str) -> dict:
    """Load configuration from YAML file."""
    config_file = Path("configs/model_configs.yaml")
    
    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_file}")
    
    with open(config_file, 'r') as f:
        configs = yaml.safe_load(f)
    
    if config_name not in configs:
        available_configs = list(configs.keys())
        raise ValueError(f"Configuration '{config_name}' not found. Available: {available_configs}")
    
    return configs[config_name]


def create_model_config(config_dict: dict) -> ModelConfig:
    """Create ModelConfig from dictionary."""
    return ModelConfig(
        model_name=config_dict.get('model_name', 'microsoft/longformer-base-4096'),
        max_length=config_dict.get('max_length', 4096),
        num_risk_categories=config_dict.get('num_risk_categories', 12),
        num_severity_levels=config_dict.get('num_severity_levels', 4),
        dropout_rate=config_dict.get('dropout_rate', 0.1),
        learning_rate=config_dict.get('learning_rate', 2e-5),
        warmup_steps=config_dict.get('warmup_steps', 500),
        weight_decay=config_dict.get('weight_decay', 0.01),
        use_hierarchical=config_dict.get('use_hierarchical', False),
        chunk_size=config_dict.get('chunk_size', 512),
        chunk_overlap=config_dict.get('chunk_overlap', 50)
    )


def main():
    parser = argparse.ArgumentParser(description="Train risk clause detection model")
    
    # Model configuration
    parser.add_argument(
        '--config', 
        type=str, 
        default='longformer_base',
        help='Model configuration name (from configs/model_configs.yaml)'
    )
    
    # Data arguments
    parser.add_argument(
        '--data_dir', 
        type=str, 
        default='data/raw/',
        help='Directory containing CUAD dataset'
    )
    parser.add_argument(
        '--taxonomy_file', 
        type=str, 
        default='data/risk_taxonomy/training_labels.csv',
        help='Risk taxonomy mapping file'
    )
    
    # Training arguments
    parser.add_argument(
        '--output_dir', 
        type=str, 
        default='outputs/',
        help='Output directory for model and logs'
    )
    parser.add_argument(
        '--epochs', 
        type=int, 
        help='Number of training epochs (overrides config)'
    )
    parser.add_argument(
        '--batch_size', 
        type=int, 
        help='Batch size (overrides config)'
    )
    parser.add_argument(
        '--learning_rate', 
        type=float, 
        help='Learning rate (overrides config)'
    )
    
    # Experiment tracking
    parser.add_argument(
        '--use_wandb', 
        action='store_true',
        help='Use Weights & Biases for experiment tracking'
    )
    parser.add_argument(
        '--project_name', 
        type=str, 
        default='risk-clause-detection',
        help='W&B project name'
    )
    parser.add_argument(
        '--run_name', 
        type=str,
        help='W&B run name'
    )
    
    # Data splits
    parser.add_argument(
        '--train_ratio', 
        type=float, 
        default=0.7,
        help='Training data ratio'
    )
    parser.add_argument(
        '--val_ratio', 
        type=float, 
        default=0.15,
        help='Validation data ratio'
    )
    parser.add_argument(
        '--test_ratio', 
        type=float, 
        default=0.15,
        help='Test data ratio'
    )
    
    # Other options
    parser.add_argument(
        '--seed', 
        type=int, 
        default=42,
        help='Random seed'
    )
    parser.add_argument(
        '--eval_only', 
        action='store_true',
        help='Only run evaluation (skip training)'
    )
    parser.add_argument(
        '--resume_from_checkpoint', 
        type=str,
        help='Resume training from checkpoint'
    )
    
    args = parser.parse_args()
    
    # Set random seed
    torch.manual_seed(args.seed)
    
    # Load configuration
    logger.info(f"Loading configuration: {args.config}")
    config_dict = load_config(args.config)
    
    # Override config with command line arguments
    if args.epochs:
        config_dict['num_epochs'] = args.epochs
    if args.batch_size:
        config_dict['batch_size'] = args.batch_size
    if args.learning_rate:
        config_dict['learning_rate'] = args.learning_rate
    
    # Create model config
    model_config = create_model_config(config_dict)
    
    logger.info(f"Model configuration:")
    for key, value in model_config.__dict__.items():
        logger.info(f"  {key}: {value}")
    
    # Check GPU availability
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    if torch.cuda.is_available():
        logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
        logger.info(f"Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Load data
    logger.info("Loading CUAD dataset...")
    loader = CUADLoader(args.data_dir)
    
    try:
        processed_docs = loader.load_processed_dataset()
        logger.info(f"Loaded {len(processed_docs)} documents")
    except Exception as e:
        logger.error(f"Failed to load CUAD dataset: {e}")
        logger.error("Please ensure CUAD v1 is downloaded to the data directory")
        return 1
    
    # Load risk taxonomy
    logger.info("Loading risk taxonomy...")
    try:
        risk_taxonomy = load_risk_taxonomy(args.taxonomy_file)
        logger.info(f"Loaded {len(risk_taxonomy)} risk mappings")
    except Exception as e:
        logger.error(f"Failed to load risk taxonomy: {e}")
        logger.error("Please run the risk mapping notebook first")
        return 1
    
    # Create data splits
    logger.info("Creating data splits...")
    train_docs, val_docs, test_docs = create_data_splits(
        processed_docs,
        train_ratio=args.train_ratio,
        val_ratio=args.val_ratio,
        test_ratio=args.test_ratio,
        random_seed=args.seed
    )
    
    # Create tokenizer
    from transformers import AutoTokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_config.model_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Create data module
    logger.info("Creating data module...")
    data_module = RiskDataModule(
        train_data=train_docs,
        val_data=val_docs,
        test_data=test_docs,
        tokenizer=tokenizer,
        risk_taxonomy=risk_taxonomy,
        batch_size=config_dict.get('batch_size', 4),
        max_length=model_config.max_length,
        num_workers=config_dict.get('num_workers', 2)
    )
    
    # Create output directory
    output_dir = Path(args.output_dir) / args.config
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize W&B if requested
    if args.use_wandb:
        import wandb
        wandb.init(
            project=args.project_name,
            name=args.run_name or f"{args.config}-{model_config.model_name.split('/')[-1]}",
            config={**model_config.__dict__, **config_dict}
        )
    
    # Create trainer
    logger.info("Creating trainer...")
    trainer = create_trainer(
        model_config=model_config,
        data_module=data_module,
        output_dir=str(output_dir),
        use_wandb=args.use_wandb
    )
    
    # Move model to GPU
    trainer.model.to(device)
    
    if not args.eval_only:
        # Train model
        logger.info("Starting training...")
        try:
            if args.resume_from_checkpoint:
                train_metrics = trainer.trainer.train(resume_from_checkpoint=args.resume_from_checkpoint)
            else:
                train_metrics = trainer.train()
            
            logger.info("Training completed successfully!")
            
            # Save training metrics
            with open(output_dir / "final_train_metrics.json", 'w') as f:
                json.dump(train_metrics.metrics, f, indent=2)
                
        except Exception as e:
            logger.error(f"Training failed: {e}")
            return 1
    
    # Evaluate model
    logger.info("Evaluating model...")
    try:
        val_metrics = trainer.evaluate("validation")
        logger.info("Validation evaluation completed")
        
        if test_docs:
            test_metrics = trainer.evaluate("test")
            logger.info("Test evaluation completed")
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        return 1
    
    # Save final model
    logger.info("Saving final model...")
    trainer.save_model(str(output_dir / "final_model"))
    
    # Test predictions on sample texts
    logger.info("Testing sample predictions...")
    sample_texts = [
        "This agreement shall be governed by the laws of Delaware.",
        "The company shall indemnify the client against all claims and damages.",
        "Either party may terminate this agreement with 30 days notice."
    ]
    
    try:
        predictions = trainer.predict(sample_texts)
        
        logger.info("Sample predictions:")
        for i, (text, pred) in enumerate(zip(sample_texts, predictions)):
            logger.info(f"  Text {i+1}: {text}")
            logger.info(f"    Risk Category: {pred.get('risk_category', 'Unknown')}")
            logger.info(f"    Confidence: {pred.get('risk_category_confidence', 0):.3f}")
            logger.info(f"    Is Risk: {pred.get('is_risk', False)}")
    
    except Exception as e:
        logger.warning(f"Sample prediction failed: {e}")
    
    logger.info(f"Training completed! Model saved to: {output_dir}")
    
    if args.use_wandb:
        wandb.finish()
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
