# Model Configuration Files for Risk Clause Detection

# Longformer Configuration (Recommended for most use cases)
longformer_base:
  model_name: "microsoft/longformer-base-4096"
  max_length: 4096
  num_risk_categories: 12
  num_severity_levels: 4
  dropout_rate: 0.1
  learning_rate: 2e-5
  warmup_steps: 500
  weight_decay: 0.01
  batch_size: 4
  num_epochs: 3
  gradient_accumulation_steps: 2

# Longformer Large (Better performance, more memory)
longformer_large:
  model_name: "microsoft/longformer-large-4096"
  max_length: 4096
  num_risk_categories: 12
  num_severity_levels: 4
  dropout_rate: 0.1
  learning_rate: 1e-5
  warmup_steps: 1000
  weight_decay: 0.01
  batch_size: 2
  num_epochs: 3
  gradient_accumulation_steps: 4

# LED Configuration (For very long documents)
led_base:
  model_name: "allenai/led-base-16384"
  max_length: 16384
  num_risk_categories: 12
  num_severity_levels: 4
  dropout_rate: 0.1
  learning_rate: 3e-5
  warmup_steps: 500
  weight_decay: 0.01
  batch_size: 1
  num_epochs: 3
  gradient_accumulation_steps: 8

# Legal-BERT Configuration (Domain-adapted)
legal_bert:
  model_name: "nlpaueb/legal-bert-base-uncased"
  max_length: 512
  num_risk_categories: 12
  num_severity_levels: 4
  dropout_rate: 0.1
  learning_rate: 2e-5
  warmup_steps: 500
  weight_decay: 0.01
  batch_size: 8
  num_epochs: 5
  use_hierarchical: true
  chunk_size: 512
  chunk_overlap: 50

# FinBERT Configuration (Financial domain)
finbert:
  model_name: "ProsusAI/finbert"
  max_length: 512
  num_risk_categories: 12
  num_severity_levels: 4
  dropout_rate: 0.1
  learning_rate: 2e-5
  warmup_steps: 500
  weight_decay: 0.01
  batch_size: 8
  num_epochs: 5
  use_hierarchical: true
  chunk_size: 512
  chunk_overlap: 50

# RoBERTa Configuration (Baseline)
roberta_base:
  model_name: "roberta-base"
  max_length: 512
  num_risk_categories: 12
  num_severity_levels: 4
  dropout_rate: 0.1
  learning_rate: 2e-5
  warmup_steps: 500
  weight_decay: 0.01
  batch_size: 8
  num_epochs: 3
  use_hierarchical: true
  chunk_size: 512
  chunk_overlap: 50

# Training Configuration
training:
  output_dir: "outputs/"
  logging_steps: 100
  eval_steps: 500
  save_steps: 500
  evaluation_strategy: "steps"
  save_strategy: "steps"
  load_best_model_at_end: true
  metric_for_best_model: "eval_f1_macro"
  greater_is_better: true
  early_stopping_patience: 3
  gradient_checkpointing: true
  fp16: true
  dataloader_pin_memory: true
  remove_unused_columns: false

# Data Configuration
data:
  train_ratio: 0.7
  val_ratio: 0.15
  test_ratio: 0.15
  random_seed: 42
  chunk_long_docs: true
  include_spans: true
  num_workers: 4

# Risk Taxonomy Configuration
risk_taxonomy:
  categories:
    - "Market Risk"
    - "Credit Risk"
    - "Operational Risk"
    - "Liquidity Risk"
    - "Regulatory Risk"
    - "Strategic Risk"
    - "Reputational Risk"
    - "Technology Risk"
    - "Supply Chain Risk"
    - "Legal Risk"
    - "Environmental Risk"
    - "Governance Risk"
  
  severity_levels:
    - "Low"
    - "Medium"
    - "High"
    - "Critical"
  
  high_risk_threshold: 3  # Severity >= 3 is considered high risk

# Evaluation Configuration
evaluation:
  metrics:
    - "f1_macro"
    - "f1_weighted"
    - "accuracy"
    - "precision_macro"
    - "recall_macro"
    - "auc_macro"
  
  span_metrics:
    - "exact_match"
    - "partial_match"
    - "span_f1"
  
  confidence_threshold: 0.5
  
# Inference Configuration
inference:
  batch_size: 1
  return_spans: true
  confidence_threshold: 0.5
  max_span_length: 200
  device: "auto"  # auto, cpu, cuda
