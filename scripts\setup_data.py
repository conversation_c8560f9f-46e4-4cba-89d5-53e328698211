#!/usr/bin/env python3
"""
Data Setup Script

Instructions for downloading and organizing the CUAD dataset.
Run this after downloading CUAD v1 from the official source.
"""

import os
import shutil
from pathlib import Path
import zipfile
import requests
from tqdm import tqdm

def download_file(url: str, filename: str):
    """Download a file with progress bar."""
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    
    with open(filename, 'wb') as file, tqdm(
        desc=filename,
        total=total_size,
        unit='B',
        unit_scale=True,
        unit_divisor=1024,
    ) as bar:
        for chunk in response.iter_content(chunk_size=8192):
            size = file.write(chunk)
            bar.update(size)

def setup_cuad_dataset():
    """
    Setup CUAD dataset structure.
    
    Note: You need to manually download CUAD v1 from:
    https://www.atticusprojectai.org/cuad
    
    Expected structure after download:
    data/raw/
    ├── CUAD_v1.json
    ├── master_clauses.csv
    ├── full_contract_txt/
    │   ├── Part_I/
    │   └── Part_II/
    └── label_group_xlsx/
    """
    
    data_dir = Path("data/raw")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    print("CUAD Dataset Setup")
    print("=" * 50)
    print()
    print("Please download CUAD v1 manually from:")
    print("https://www.atticusprojectai.org/cuad")
    print()
    print("After downloading, extract the files to:")
    print(f"  {data_dir.absolute()}")
    print()
    print("Expected files:")
    print("  ✓ CUAD_v1.json")
    print("  ✓ master_clauses.csv (or .xlsx)")
    print("  ✓ full_contract_txt/Part_I/")
    print("  ✓ full_contract_txt/Part_II/")
    print("  ✓ label_group_xlsx/")
    print()
    
    # Check if files exist
    required_files = [
        "CUAD_v1.json",
        "master_clauses.csv",
        "full_contract_txt",
        "label_group_xlsx"
    ]
    
    print("Checking for required files...")
    all_present = True
    for file_path in required_files:
        full_path = data_dir / file_path
        if full_path.exists():
            print(f"  ✓ {file_path}")
        else:
            print(f"  ✗ {file_path} (missing)")
            all_present = False
    
    if all_present:
        print("\n🎉 All required files found!")
        print("You can now run the data exploration notebook.")
        
        # Test loading
        try:
            from src.data.cuad_loader import CUADLoader
            loader = CUADLoader()
            stats = loader.get_dataset_statistics()
            print(f"\nDataset loaded successfully:")
            print(f"  Documents: {stats['total_documents']}")
            print(f"  Clauses: {stats['total_clauses']}")
            print(f"  Clause types: {stats['unique_clause_types']}")
        except Exception as e:
            print(f"\n⚠️  Error loading dataset: {e}")
    else:
        print("\n❌ Some files are missing. Please download and extract CUAD v1.")

def create_sample_data():
    """Create sample data structure for testing (without actual CUAD data)."""
    print("Creating sample data structure for testing...")
    
    data_dir = Path("data/raw")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    # Create sample directories
    (data_dir / "full_contract_txt" / "Part_I").mkdir(parents=True, exist_ok=True)
    (data_dir / "full_contract_txt" / "Part_II").mkdir(parents=True, exist_ok=True)
    (data_dir / "label_group_xlsx").mkdir(parents=True, exist_ok=True)
    
    # Create sample JSON structure
    sample_data = {
        "version": "v1.0",
        "data": [
            {
                "title": "sample_contract.txt",
                "paragraphs": [
                    {
                        "context": "This is a sample contract clause for testing purposes.",
                        "qas": [
                            {
                                "question": "Governing Law",
                                "answers": [
                                    {
                                        "text": "This agreement shall be governed by the laws of Delaware",
                                        "answer_start": 0
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    # Save sample JSON
    import json
    with open(data_dir / "CUAD_v1.json", 'w') as f:
        json.dump(sample_data, f, indent=2)
    
    # Create sample CSV
    import pandas as pd
    sample_clauses = pd.DataFrame({
        'clause_type': ['Governing Law', 'Termination', 'Warranty'],
        'count': [150, 200, 100],
        'description': [
            'Clauses specifying governing law',
            'Contract termination conditions', 
            'Warranty and liability clauses'
        ]
    })
    sample_clauses.to_csv(data_dir / "master_clauses.csv", index=False)
    
    # Create sample contract text
    sample_contract = """
SAMPLE CONTRACT AGREEMENT

This agreement shall be governed by the laws of Delaware.

The parties may terminate this agreement with 30 days notice.

The company provides no warranty beyond what is required by law.
"""
    
    with open(data_dir / "full_contract_txt" / "Part_I" / "sample_contract.txt", 'w') as f:
        f.write(sample_contract)
    
    print("✓ Sample data structure created")
    print("  Note: This is just for testing the pipeline structure")
    print("  You still need to download the real CUAD dataset")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--sample":
        create_sample_data()
    else:
        setup_cuad_dataset()
