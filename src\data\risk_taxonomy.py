"""
Risk Taxonomy Mapping System

Maps CUAD clause types to financial risk categories for risk detection pipeline.
Based on standard risk frameworks used in financial analysis and corporate disclosures.
"""

import json
import pandas as pd
from typing import Dict, List, Tuple, Optional
from pathlib import Path
from enum import Enum
import re

class RiskCategory(Enum):
    """Standard financial risk categories."""
    MARKET_RISK = "Market Risk"
    CREDIT_RISK = "Credit Risk" 
    OPERATIONAL_RISK = "Operational Risk"
    LIQUIDITY_RISK = "Liquidity Risk"
    REGULATORY_RISK = "Regulatory Risk"
    STRATEGIC_RISK = "Strategic Risk"
    REPUTATIONAL_RISK = "Reputational Risk"
    TECHNOLOGY_RISK = "Technology Risk"
    SUPPLY_CHAIN_RISK = "Supply Chain Risk"
    LEGAL_RISK = "Legal Risk"
    ENVIRONMENTAL_RISK = "Environmental Risk"
    GOVERNANCE_RISK = "Governance Risk"
    OTHER_RISK = "Other Risk"

class RiskSeverity(Enum):
    """Risk severity levels."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class RiskTaxonomyMapper:
    """Maps CUAD clause types to financial risk categories."""
    
    def __init__(self):
        """Initialize with predefined mapping rules."""
        self.clause_to_risk_mapping = self._create_base_mapping()
        self.risk_keywords = self._create_keyword_mapping()
        
    def _create_base_mapping(self) -> Dict[str, Dict]:
        """Create base mapping from CUAD clause types to risk categories."""
        return {
            # Legal & Compliance Risks
            "Governing Law": {
                "category": RiskCategory.LEGAL_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Legal jurisdiction and governing law risks"
            },
            "Dispute Resolution": {
                "category": RiskCategory.LEGAL_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Dispute resolution mechanism risks"
            },
            "Covenant not to Sue": {
                "category": RiskCategory.LEGAL_RISK,
                "severity": RiskSeverity.LOW,
                "description": "Legal action limitation agreements"
            },
            
            # Operational Risks
            "Termination": {
                "category": RiskCategory.OPERATIONAL_RISK,
                "severity": RiskSeverity.HIGH,
                "description": "Contract termination and business continuity risks"
            },
            "Change of Control": {
                "category": RiskCategory.STRATEGIC_RISK,
                "severity": RiskSeverity.HIGH,
                "description": "Ownership change and control transfer risks"
            },
            "Anti-Assignment": {
                "category": RiskCategory.OPERATIONAL_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Assignment restriction and operational flexibility risks"
            },
            
            # Financial & Credit Risks
            "Cap on Liability": {
                "category": RiskCategory.CREDIT_RISK,
                "severity": RiskSeverity.HIGH,
                "description": "Liability limitation and financial exposure risks"
            },
            "Liquidated Damages": {
                "category": RiskCategory.CREDIT_RISK,
                "severity": RiskSeverity.HIGH,
                "description": "Predetermined damage payment risks"
            },
            "Uncapped Liability": {
                "category": RiskCategory.CREDIT_RISK,
                "severity": RiskSeverity.CRITICAL,
                "description": "Unlimited liability exposure risks"
            },
            
            # Technology & IP Risks
            "IP Ownership Assignment": {
                "category": RiskCategory.TECHNOLOGY_RISK,
                "severity": RiskSeverity.HIGH,
                "description": "Intellectual property ownership and transfer risks"
            },
            "License Grant": {
                "category": RiskCategory.TECHNOLOGY_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Technology licensing and usage rights risks"
            },
            "Non-Compete": {
                "category": RiskCategory.STRATEGIC_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Competitive restriction and market access risks"
            },
            
            # Supply Chain & Vendor Risks
            "Exclusivity": {
                "category": RiskCategory.SUPPLY_CHAIN_RISK,
                "severity": RiskSeverity.HIGH,
                "description": "Exclusive dealing and supplier dependency risks"
            },
            "No-Solicit of Customers": {
                "category": RiskCategory.STRATEGIC_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Customer relationship and market access risks"
            },
            "No-Solicit of Employees": {
                "category": RiskCategory.OPERATIONAL_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Employee retention and talent acquisition risks"
            },
            
            # Regulatory & Compliance Risks
            "Compliance with Laws": {
                "category": RiskCategory.REGULATORY_RISK,
                "severity": RiskSeverity.HIGH,
                "description": "Legal compliance and regulatory adherence risks"
            },
            "GDPR": {
                "category": RiskCategory.REGULATORY_RISK,
                "severity": RiskSeverity.HIGH,
                "description": "Data protection and privacy regulation risks"
            },
            
            # Performance & Quality Risks
            "Warranty Duration": {
                "category": RiskCategory.OPERATIONAL_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Product warranty and quality assurance risks"
            },
            "Insurance": {
                "category": RiskCategory.OPERATIONAL_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Insurance coverage and risk transfer mechanisms"
            },
            
            # Financial Terms & Conditions
            "Revenue/Profit Sharing": {
                "category": RiskCategory.CREDIT_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Revenue sharing and profit distribution risks"
            },
            "Price Restrictions": {
                "category": RiskCategory.MARKET_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Pricing flexibility and market positioning risks"
            },

            # Force Majeure & External Risks
            "Force Majeure": {
                "category": RiskCategory.OPERATIONAL_RISK,
                "severity": RiskSeverity.HIGH,
                "description": "Unforeseeable circumstances and business disruption risks"
            },

            # Additional common CUAD clause mappings
            "Indemnification": {
                "category": RiskCategory.CREDIT_RISK,
                "severity": RiskSeverity.HIGH,
                "description": "Indemnification obligations and financial liability risks"
            },
            "Confidentiality": {
                "category": RiskCategory.TECHNOLOGY_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Information security and confidentiality breach risks"
            },
            "Most Favored Nation": {
                "category": RiskCategory.MARKET_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Pricing and terms competitiveness risks"
            },
            "Minimum Commitment": {
                "category": RiskCategory.OPERATIONAL_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Performance commitment and delivery obligation risks"
            },
            "Volume Restriction": {
                "category": RiskCategory.SUPPLY_CHAIN_RISK,
                "severity": RiskSeverity.MEDIUM,
                "description": "Volume limitation and supply capacity risks"
            }
        }
    
    def _create_keyword_mapping(self) -> Dict[RiskCategory, List[str]]:
        """Create keyword-based mapping for unmapped clauses."""
        return {
            RiskCategory.LEGAL_RISK: [
                'legal', 'law', 'court', 'jurisdiction', 'litigation', 'sue', 'lawsuit',
                'attorney', 'counsel', 'dispute', 'arbitration', 'mediation'
            ],
            RiskCategory.CREDIT_RISK: [
                'liability', 'damage', 'loss', 'payment', 'financial', 'money',
                'compensation', 'indemnif', 'reimburse', 'penalty', 'fine'
            ],
            RiskCategory.OPERATIONAL_RISK: [
                'operation', 'business', 'service', 'performance', 'delivery',
                'termination', 'breach', 'default', 'failure', 'disruption'
            ],
            RiskCategory.REGULATORY_RISK: [
                'compliance', 'regulation', 'regulatory', 'government', 'authority',
                'permit', 'license', 'approval', 'certification', 'audit'
            ],
            RiskCategory.TECHNOLOGY_RISK: [
                'technology', 'software', 'system', 'data', 'intellectual', 'patent',
                'copyright', 'trademark', 'proprietary', 'confidential'
            ],
            RiskCategory.STRATEGIC_RISK: [
                'strategic', 'compete', 'market', 'customer', 'business model',
                'partnership', 'alliance', 'merger', 'acquisition', 'control'
            ],
            RiskCategory.SUPPLY_CHAIN_RISK: [
                'supply', 'vendor', 'supplier', 'procurement', 'sourcing',
                'delivery', 'logistics', 'inventory', 'material', 'component'
            ],
            RiskCategory.ENVIRONMENTAL_RISK: [
                'environment', 'environmental', 'sustainability', 'climate',
                'carbon', 'emission', 'waste', 'pollution', 'green'
            ]
        }
    
    def map_clause_to_risk(self, clause_type: str) -> Dict:
        """
        Map a CUAD clause type to risk category.
        
        Args:
            clause_type: CUAD clause type string
            
        Returns:
            Dictionary with risk category, severity, and description
        """
        # Direct mapping
        if clause_type in self.clause_to_risk_mapping:
            mapping = self.clause_to_risk_mapping[clause_type]
            return {
                'clause_type': clause_type,
                'risk_category': mapping['category'].value,
                'risk_severity': mapping['severity'].value,
                'description': mapping['description'],
                'mapping_method': 'direct'
            }
        
        # Keyword-based mapping
        clause_lower = clause_type.lower()
        best_match = None
        max_matches = 0
        
        for risk_category, keywords in self.risk_keywords.items():
            matches = sum(1 for keyword in keywords if keyword in clause_lower)
            if matches > max_matches:
                max_matches = matches
                best_match = risk_category
        
        if best_match and max_matches > 0:
            return {
                'clause_type': clause_type,
                'risk_category': best_match.value,
                'risk_severity': RiskSeverity.MEDIUM.value,  # Default to medium
                'description': f"Keyword-based mapping to {best_match.value}",
                'mapping_method': 'keyword',
                'keyword_matches': max_matches
            }
        
        # Default mapping
        return {
            'clause_type': clause_type,
            'risk_category': RiskCategory.OTHER_RISK.value,
            'risk_severity': RiskSeverity.LOW.value,
            'description': "Unmapped clause type",
            'mapping_method': 'default'
        }
    
    def create_risk_taxonomy_dataset(self, cuad_clause_types: List[str]) -> pd.DataFrame:
        """
        Create complete risk taxonomy mapping dataset.
        
        Args:
            cuad_clause_types: List of all CUAD clause types
            
        Returns:
            DataFrame with risk mappings for all clause types
        """
        mappings = []
        for clause_type in cuad_clause_types:
            mapping = self.map_clause_to_risk(clause_type)
            mappings.append(mapping)
        
        return pd.DataFrame(mappings)
    
    def get_risk_category_distribution(self, mappings_df: pd.DataFrame) -> Dict:
        """Get distribution of risk categories."""
        return mappings_df['risk_category'].value_counts().to_dict()
    
    def export_taxonomy(self, mappings_df: pd.DataFrame, output_dir: str = "data/risk_taxonomy/"):
        """Export risk taxonomy to files."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Export main mapping
        mappings_df.to_csv(output_path / "cuad_risk_mapping.csv", index=False)
        
        # Export by risk category
        for category in RiskCategory:
            category_df = mappings_df[mappings_df['risk_category'] == category.value]
            if not category_df.empty:
                filename = f"risk_category_{category.name.lower()}.csv"
                category_df.to_csv(output_path / filename, index=False)
        
        # Export summary statistics
        stats = {
            'total_clause_types': len(mappings_df),
            'risk_categories': len(mappings_df['risk_category'].unique()),
            'mapping_methods': mappings_df['mapping_method'].value_counts().to_dict(),
            'risk_distribution': self.get_risk_category_distribution(mappings_df),
            'severity_distribution': mappings_df['risk_severity'].value_counts().to_dict()
        }
        
        with open(output_path / "taxonomy_statistics.json", 'w') as f:
            json.dump(stats, f, indent=2)
        
        print(f"Risk taxonomy exported to {output_path}")
        return stats


def main():
    """Example usage of risk taxonomy mapper."""
    # Sample CUAD clause types for testing
    sample_clauses = [
        "Governing Law", "Termination", "Cap on Liability", "IP Ownership Assignment",
        "Force Majeure", "Compliance with Laws", "Non-Compete", "Insurance",
        "Warranty Duration", "Liquidated Damages", "Change of Control"
    ]
    
    mapper = RiskTaxonomyMapper()
    
    # Create mappings
    mappings_df = mapper.create_risk_taxonomy_dataset(sample_clauses)
    
    print("Risk Taxonomy Mapping Results:")
    print("=" * 50)
    print(mappings_df.to_string(index=False))
    
    # Export results
    stats = mapper.export_taxonomy(mappings_df)
    print(f"\nTaxonomy Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
