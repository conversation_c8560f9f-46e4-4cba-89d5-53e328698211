"""
Evaluation metrics for risk clause detection.

Implements specialized metrics for imbalanced classification,
span-level evaluation, and risk assessment quality.
"""

import numpy as np
import pandas as pd
from sklearn.metrics import (
    classification_report, confusion_matrix, f1_score,
    precision_recall_fscore_support, roc_auc_score, average_precision_score
)
from typing import Dict, List, Tuple, Optional, Union
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)


class RiskEvaluationMetrics:
    """Comprehensive evaluation metrics for risk clause detection."""
    
    def __init__(self, risk_categories: List[str], severity_levels: List[str]):
        """
        Initialize evaluation metrics.
        
        Args:
            risk_categories: List of risk category names
            severity_levels: List of severity level names
        """
        self.risk_categories = risk_categories
        self.severity_levels = severity_levels
    
    def compute_classification_metrics(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray,
        y_proba: Optional[np.ndarray] = None,
        task_name: str = "risk_classification"
    ) -> Dict[str, float]:
        """
        Compute comprehensive classification metrics.
        
        Args:
            y_true: True labels
            y_pred: Predicted labels
            y_proba: Prediction probabilities (optional)
            task_name: Name of the classification task
            
        Returns:
            Dictionary of metrics
        """
        metrics = {}
        
        # Basic metrics
        precision, recall, f1, support = precision_recall_fscore_support(
            y_true, y_pred, average=None, zero_division=0
        )
        
        # Macro/micro/weighted averages
        metrics.update({
            f'{task_name}_f1_macro': f1_score(y_true, y_pred, average='macro'),
            f'{task_name}_f1_micro': f1_score(y_true, y_pred, average='micro'),
            f'{task_name}_f1_weighted': f1_score(y_true, y_pred, average='weighted'),
            f'{task_name}_accuracy': (y_pred == y_true).mean()
        })
        
        # Per-class metrics
        for i, (p, r, f, s) in enumerate(zip(precision, recall, f1, support)):
            class_name = self.risk_categories[i] if i < len(self.risk_categories) else f"class_{i}"
            metrics.update({
                f'{task_name}_precision_{class_name}': p,
                f'{task_name}_recall_{class_name}': r,
                f'{task_name}_f1_{class_name}': f,
                f'{task_name}_support_{class_name}': s
            })
        
        # AUC metrics if probabilities provided
        if y_proba is not None:
            try:
                # Multi-class AUC (one-vs-rest)
                auc_scores = []
                for i in range(y_proba.shape[1]):
                    y_true_binary = (y_true == i).astype(int)
                    auc = roc_auc_score(y_true_binary, y_proba[:, i])
                    auc_scores.append(auc)
                    
                    class_name = self.risk_categories[i] if i < len(self.risk_categories) else f"class_{i}"
                    metrics[f'{task_name}_auc_{class_name}'] = auc
                
                metrics[f'{task_name}_auc_macro'] = np.mean(auc_scores)
                
                # Average precision (AP) scores
                ap_scores = []
                for i in range(y_proba.shape[1]):
                    y_true_binary = (y_true == i).astype(int)
                    ap = average_precision_score(y_true_binary, y_proba[:, i])
                    ap_scores.append(ap)
                
                metrics[f'{task_name}_ap_macro'] = np.mean(ap_scores)
                
            except Exception as e:
                logger.warning(f"Could not compute AUC metrics: {e}")
        
        return metrics
    
    def compute_span_metrics(
        self,
        true_spans: List[Tuple[int, int]],
        pred_spans: List[Tuple[int, int]],
        texts: List[str]
    ) -> Dict[str, float]:
        """
        Compute span-level evaluation metrics.
        
        Args:
            true_spans: List of true span positions (start, end)
            pred_spans: List of predicted span positions
            texts: List of input texts
            
        Returns:
            Dictionary of span metrics
        """
        if len(true_spans) != len(pred_spans):
            raise ValueError("Number of true and predicted spans must match")
        
        exact_matches = 0
        partial_matches = 0
        total_overlap = 0
        
        for true_span, pred_span, text in zip(true_spans, pred_spans, texts):
            if true_span == pred_span:
                exact_matches += 1
                partial_matches += 1
                total_overlap += 1
            else:
                # Calculate overlap
                overlap = self._calculate_span_overlap(true_span, pred_span)
                if overlap > 0:
                    partial_matches += 1
                    total_overlap += overlap
        
        n_spans = len(true_spans)
        
        return {
            'span_exact_match': exact_matches / n_spans if n_spans > 0 else 0,
            'span_partial_match': partial_matches / n_spans if n_spans > 0 else 0,
            'span_avg_overlap': total_overlap / n_spans if n_spans > 0 else 0,
            'span_precision': self._calculate_span_precision(true_spans, pred_spans),
            'span_recall': self._calculate_span_recall(true_spans, pred_spans),
            'span_f1': self._calculate_span_f1(true_spans, pred_spans)
        }
    
    def _calculate_span_overlap(self, span1: Tuple[int, int], span2: Tuple[int, int]) -> float:
        """Calculate overlap ratio between two spans."""
        start1, end1 = span1
        start2, end2 = span2
        
        # Calculate intersection
        intersection_start = max(start1, start2)
        intersection_end = min(end1, end2)
        
        if intersection_start >= intersection_end:
            return 0.0
        
        intersection_length = intersection_end - intersection_start
        union_length = max(end1, end2) - min(start1, start2)
        
        return intersection_length / union_length if union_length > 0 else 0.0
    
    def _calculate_span_precision(self, true_spans: List[Tuple[int, int]], pred_spans: List[Tuple[int, int]]) -> float:
        """Calculate span-level precision."""
        if not pred_spans:
            return 0.0
        
        correct = 0
        for pred_span in pred_spans:
            for true_span in true_spans:
                if self._calculate_span_overlap(pred_span, true_span) > 0.5:
                    correct += 1
                    break
        
        return correct / len(pred_spans)
    
    def _calculate_span_recall(self, true_spans: List[Tuple[int, int]], pred_spans: List[Tuple[int, int]]) -> float:
        """Calculate span-level recall."""
        if not true_spans:
            return 0.0
        
        correct = 0
        for true_span in true_spans:
            for pred_span in pred_spans:
                if self._calculate_span_overlap(true_span, pred_span) > 0.5:
                    correct += 1
                    break
        
        return correct / len(true_spans)
    
    def _calculate_span_f1(self, true_spans: List[Tuple[int, int]], pred_spans: List[Tuple[int, int]]) -> float:
        """Calculate span-level F1 score."""
        precision = self._calculate_span_precision(true_spans, pred_spans)
        recall = self._calculate_span_recall(true_spans, pred_spans)
        
        if precision + recall == 0:
            return 0.0
        
        return 2 * (precision * recall) / (precision + recall)
    
    def compute_risk_assessment_metrics(
        self,
        true_risks: List[Dict],
        pred_risks: List[Dict]
    ) -> Dict[str, float]:
        """
        Compute risk assessment quality metrics.
        
        Args:
            true_risks: List of true risk assessments
            pred_risks: List of predicted risk assessments
            
        Returns:
            Dictionary of risk assessment metrics
        """
        if len(true_risks) != len(pred_risks):
            raise ValueError("Number of true and predicted risks must match")
        
        severity_accuracy = 0
        category_accuracy = 0
        high_risk_precision = 0
        high_risk_recall = 0
        
        true_high_risk = 0
        pred_high_risk = 0
        correct_high_risk = 0
        
        for true_risk, pred_risk in zip(true_risks, pred_risks):
            # Severity accuracy
            if true_risk.get('severity') == pred_risk.get('severity'):
                severity_accuracy += 1
            
            # Category accuracy
            if true_risk.get('category') == pred_risk.get('category'):
                category_accuracy += 1
            
            # High-risk detection
            true_is_high = true_risk.get('severity', 0) >= 3
            pred_is_high = pred_risk.get('severity', 0) >= 3
            
            if true_is_high:
                true_high_risk += 1
                if pred_is_high:
                    correct_high_risk += 1
            
            if pred_is_high:
                pred_high_risk += 1
        
        n_risks = len(true_risks)
        
        # Calculate high-risk precision and recall
        if pred_high_risk > 0:
            high_risk_precision = correct_high_risk / pred_high_risk
        if true_high_risk > 0:
            high_risk_recall = correct_high_risk / true_high_risk
        
        # High-risk F1
        if high_risk_precision + high_risk_recall > 0:
            high_risk_f1 = 2 * (high_risk_precision * high_risk_recall) / (high_risk_precision + high_risk_recall)
        else:
            high_risk_f1 = 0
        
        return {
            'severity_accuracy': severity_accuracy / n_risks if n_risks > 0 else 0,
            'category_accuracy': category_accuracy / n_risks if n_risks > 0 else 0,
            'high_risk_precision': high_risk_precision,
            'high_risk_recall': high_risk_recall,
            'high_risk_f1': high_risk_f1,
            'high_risk_support': true_high_risk
        }
    
    def plot_confusion_matrix(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray,
        labels: Optional[List[str]] = None,
        title: str = "Confusion Matrix",
        figsize: Tuple[int, int] = (10, 8)
    ) -> plt.Figure:
        """Plot confusion matrix."""
        cm = confusion_matrix(y_true, y_pred)
        
        fig, ax = plt.subplots(figsize=figsize)
        
        # Use provided labels or default
        if labels is None:
            labels = [f"Class {i}" for i in range(cm.shape[0])]
        
        # Plot heatmap
        sns.heatmap(
            cm, 
            annot=True, 
            fmt='d', 
            cmap='Blues',
            xticklabels=labels,
            yticklabels=labels,
            ax=ax
        )
        
        ax.set_title(title)
        ax.set_xlabel('Predicted')
        ax.set_ylabel('True')
        
        plt.tight_layout()
        return fig
    
    def plot_class_distribution(
        self,
        y_true: np.ndarray,
        labels: Optional[List[str]] = None,
        title: str = "Class Distribution",
        figsize: Tuple[int, int] = (12, 6)
    ) -> plt.Figure:
        """Plot class distribution."""
        unique, counts = np.unique(y_true, return_counts=True)
        
        if labels is None:
            labels = [f"Class {i}" for i in unique]
        else:
            labels = [labels[i] for i in unique]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)
        
        # Bar plot
        ax1.bar(labels, counts)
        ax1.set_title(f"{title} - Counts")
        ax1.set_xlabel("Class")
        ax1.set_ylabel("Count")
        ax1.tick_params(axis='x', rotation=45)
        
        # Pie chart
        ax2.pie(counts, labels=labels, autopct='%1.1f%%')
        ax2.set_title(f"{title} - Proportions")
        
        plt.tight_layout()
        return fig
    
    def generate_evaluation_report(
        self,
        results: Dict[str, Dict],
        output_path: Optional[str] = None
    ) -> str:
        """Generate comprehensive evaluation report."""
        report_lines = [
            "Risk Clause Detection - Evaluation Report",
            "=" * 50,
            ""
        ]
        
        # Overall performance
        if 'classification' in results:
            cls_metrics = results['classification']
            report_lines.extend([
                "Classification Performance:",
                f"  Macro F1: {cls_metrics.get('f1_macro', 0):.4f}",
                f"  Weighted F1: {cls_metrics.get('f1_weighted', 0):.4f}",
                f"  Accuracy: {cls_metrics.get('accuracy', 0):.4f}",
                ""
            ])
        
        # Span extraction performance
        if 'spans' in results:
            span_metrics = results['spans']
            report_lines.extend([
                "Span Extraction Performance:",
                f"  Exact Match: {span_metrics.get('span_exact_match', 0):.4f}",
                f"  Partial Match: {span_metrics.get('span_partial_match', 0):.4f}",
                f"  Span F1: {span_metrics.get('span_f1', 0):.4f}",
                ""
            ])
        
        # Risk assessment quality
        if 'risk_assessment' in results:
            risk_metrics = results['risk_assessment']
            report_lines.extend([
                "Risk Assessment Quality:",
                f"  Severity Accuracy: {risk_metrics.get('severity_accuracy', 0):.4f}",
                f"  Category Accuracy: {risk_metrics.get('category_accuracy', 0):.4f}",
                f"  High-Risk F1: {risk_metrics.get('high_risk_f1', 0):.4f}",
                ""
            ])
        
        # Per-class performance
        if 'classification' in results:
            report_lines.append("Per-Class Performance:")
            for key, value in results['classification'].items():
                if key.startswith('f1_') and not key.endswith(('macro', 'micro', 'weighted')):
                    class_name = key.replace('f1_', '')
                    report_lines.append(f"  {class_name}: {value:.4f}")
            report_lines.append("")
        
        report = "\n".join(report_lines)
        
        # Save to file if path provided
        if output_path:
            with open(output_path, 'w') as f:
                f.write(report)
        
        return report


def evaluate_model_predictions(
    true_labels: Dict[str, np.ndarray],
    predictions: Dict[str, np.ndarray],
    probabilities: Optional[Dict[str, np.ndarray]] = None,
    risk_categories: Optional[List[str]] = None,
    severity_levels: Optional[List[str]] = None
) -> Dict[str, Dict]:
    """
    Comprehensive evaluation of model predictions.
    
    Args:
        true_labels: Dictionary of true labels for each task
        predictions: Dictionary of predictions for each task
        probabilities: Dictionary of prediction probabilities (optional)
        risk_categories: List of risk category names
        severity_levels: List of severity level names
        
    Returns:
        Dictionary of evaluation results
    """
    if risk_categories is None:
        risk_categories = [f"Risk_{i}" for i in range(12)]
    if severity_levels is None:
        severity_levels = ["Low", "Medium", "High", "Critical"]
    
    evaluator = RiskEvaluationMetrics(risk_categories, severity_levels)
    results = {}
    
    # Classification metrics
    for task in ['risk_category', 'severity', 'binary_risk']:
        if task in true_labels and task in predictions:
            y_true = true_labels[task]
            y_pred = predictions[task]
            y_proba = probabilities.get(task) if probabilities else None
            
            metrics = evaluator.compute_classification_metrics(
                y_true, y_pred, y_proba, task
            )
            results[task] = metrics
    
    return results
