# Intelligent Contract Analyzer: Risk Clause Detection

An NLP pipeline for detecting and categorizing risk clauses in financial agreements using transformer models and the CUAD dataset.

## Project Structure

```
├── data/                          # Data directory
│   ├── raw/                      # Raw CUAD dataset
│   │   ├── CUAD_v1.json         # Main annotations
│   │   ├── master_clauses.csv    # Clause metadata
│   │   ├── full_contract_txt/    # Contract text files
│   │   └── label_group_xlsx/     # Per-clause reports
│   ├── processed/                # Processed datasets
│   └── risk_taxonomy/            # Risk mapping files
├── src/                          # Source code
│   ├── data/                     # Data processing
│   ├── models/                   # Model architectures
│   ├── training/                 # Training scripts
│   ├── evaluation/               # Evaluation metrics
│   └── utils/                    # Utilities
├── notebooks/                    # Jupyter notebooks
│   ├── 01_data_exploration.ipynb
│   ├── 02_risk_mapping.ipynb
│   └── 03_model_training.ipynb
├── configs/                      # Configuration files
├── scripts/                      # Utility scripts
└── requirements.txt              # Dependencies
```

## Setup Instructions

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Download CUAD Dataset**:
   - Place CUAD v1 files in `data/raw/`
   - Run data processing scripts

3. **For GPU Training** (Colab/Kaggle):
   - Upload the entire project folder
   - Use the provided Colab notebooks
   - Models are designed for T4/P100 GPUs

## Quick Start

```python
from src.data.cuad_loader import CUADLoader
from src.models.risk_classifier import RiskClassifier

# Load data
loader = CUADLoader("data/raw/")
data = loader.load_annotations()

# Initialize model (for Colab/Kaggle)
model = RiskClassifier(model_name="microsoft/longformer-base-4096")
```

## Features

- ✅ CUAD dataset integration
- ✅ Risk taxonomy mapping
- ✅ Long-document processing (Longformer/LED)
- ✅ Explainable span extraction
- ✅ Evaluation metrics for imbalanced data
- ✅ Colab/Kaggle ready notebooks

## Models Supported

- Longformer (4k/16k tokens)
- LED (Longformer Encoder-Decoder)
- Legal-BERT + Hierarchical attention
- FinBERT for financial domain adaptation
