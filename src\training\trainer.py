"""
Training pipeline for risk clause detection models.

Implements training loop, evaluation metrics, and model checkpointing
for long-document risk classification.
"""

import torch
import torch.nn as nn
from transformers import TrainingArguments, Trainer, EarlyStoppingCallback
from sklearn.metrics import classification_report, f1_score, precision_recall_fscore_support
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
import wandb
from pathlib import Path
import json

from src.models.risk_classifier import LongDocumentRiskClassifier, ModelConfig
from src.training.dataset import RiskDataModule

logger = logging.getLogger(__name__)


class RiskClassificationTrainer:
    """Custom trainer for risk clause detection."""
    
    def __init__(
        self,
        model: LongDocumentRiskClassifier,
        data_module: RiskDataModule,
        config: ModelConfig,
        output_dir: str = "outputs/",
        use_wandb: bool = False,
        project_name: str = "risk-clause-detection"
    ):
        """
        Initialize trainer.
        
        Args:
            model: Risk classification model
            data_module: Data module with train/val/test loaders
            config: Model configuration
            output_dir: Directory for saving outputs
            use_wandb: Whether to use Weights & Biases logging
            project_name: W&B project name
        """
        self.model = model
        self.data_module = data_module
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize W&B if requested
        if use_wandb:
            wandb.init(
                project=project_name,
                config=config.__dict__,
                name=f"risk-classifier-{config.model_name.split('/')[-1]}"
            )
        
        # Setup training arguments
        self.training_args = TrainingArguments(
            output_dir=str(self.output_dir),
            num_train_epochs=3,
            per_device_train_batch_size=config.batch_size if hasattr(config, 'batch_size') else 4,
            per_device_eval_batch_size=config.batch_size if hasattr(config, 'batch_size') else 4,
            warmup_steps=config.warmup_steps,
            weight_decay=config.weight_decay,
            learning_rate=config.learning_rate,
            logging_dir=str(self.output_dir / "logs"),
            logging_steps=100,
            eval_steps=500,
            save_steps=500,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_f1_macro",
            greater_is_better=True,
            report_to="wandb" if use_wandb else None,
            dataloader_pin_memory=True,
            gradient_checkpointing=True,  # Save memory for long sequences
            fp16=torch.cuda.is_available(),  # Use mixed precision if available
            remove_unused_columns=False  # Keep all columns for custom forward pass
        )
        
        # Create HuggingFace trainer
        self.trainer = Trainer(
            model=self.model,
            args=self.training_args,
            train_dataset=self.data_module.train_dataset,
            eval_dataset=self.data_module.val_dataset,
            compute_metrics=self.compute_metrics,
            callbacks=[EarlyStoppingCallback(early_stopping_patience=3)]
        )
    
    def compute_metrics(self, eval_pred) -> Dict[str, float]:
        """Compute evaluation metrics."""
        predictions, labels = eval_pred
        
        # Handle multi-task outputs
        if isinstance(predictions, tuple):
            risk_preds, severity_preds, binary_preds = predictions[:3]
        else:
            # Single task
            risk_preds = predictions
            severity_preds = None
            binary_preds = None
        
        # Extract labels
        if isinstance(labels, tuple):
            risk_labels, severity_labels, binary_labels = labels[:3]
        else:
            risk_labels = labels
            severity_labels = None
            binary_labels = None
        
        metrics = {}
        
        # Risk category classification metrics
        if risk_preds is not None and risk_labels is not None:
            risk_pred_labels = np.argmax(risk_preds, axis=1)
            
            # Macro F1 (important for imbalanced classes)
            f1_macro = f1_score(risk_labels, risk_pred_labels, average='macro')
            f1_micro = f1_score(risk_labels, risk_pred_labels, average='micro')
            f1_weighted = f1_score(risk_labels, risk_pred_labels, average='weighted')
            
            metrics.update({
                'f1_macro': f1_macro,
                'f1_micro': f1_micro,
                'f1_weighted': f1_weighted,
                'accuracy': (risk_pred_labels == risk_labels).mean()
            })
            
            # Per-class metrics
            precision, recall, f1, support = precision_recall_fscore_support(
                risk_labels, risk_pred_labels, average=None, zero_division=0
            )
            
            for i, (p, r, f) in enumerate(zip(precision, recall, f1)):
                metrics[f'precision_class_{i}'] = p
                metrics[f'recall_class_{i}'] = r
                metrics[f'f1_class_{i}'] = f
        
        # Severity classification metrics
        if severity_preds is not None and severity_labels is not None:
            severity_pred_labels = np.argmax(severity_preds, axis=1)
            
            metrics.update({
                'severity_f1_macro': f1_score(severity_labels, severity_pred_labels, average='macro'),
                'severity_accuracy': (severity_pred_labels == severity_labels).mean()
            })
        
        # Binary risk detection metrics
        if binary_preds is not None and binary_labels is not None:
            binary_pred_labels = np.argmax(binary_preds, axis=1)
            
            metrics.update({
                'binary_f1': f1_score(binary_labels, binary_pred_labels, average='binary'),
                'binary_accuracy': (binary_pred_labels == binary_labels).mean()
            })
        
        return metrics
    
    def train(self) -> Dict:
        """Train the model."""
        logger.info("Starting training...")
        
        # Train model
        train_result = self.trainer.train()
        
        # Save model
        self.trainer.save_model()
        
        # Save training metrics
        metrics = train_result.metrics
        with open(self.output_dir / "train_metrics.json", 'w') as f:
            json.dump(metrics, f, indent=2)
        
        logger.info("Training completed!")
        return metrics
    
    def evaluate(self, dataset_name: str = "validation") -> Dict:
        """Evaluate the model."""
        logger.info(f"Evaluating on {dataset_name} set...")
        
        if dataset_name == "test" and self.data_module.test_dataset:
            eval_dataset = self.data_module.test_dataset
        else:
            eval_dataset = self.data_module.val_dataset
        
        # Evaluate
        eval_result = self.trainer.evaluate(eval_dataset=eval_dataset)
        
        # Save evaluation metrics
        with open(self.output_dir / f"{dataset_name}_metrics.json", 'w') as f:
            json.dump(eval_result, f, indent=2)
        
        # Generate detailed classification report
        predictions = self.trainer.predict(eval_dataset)
        self._generate_classification_report(predictions, dataset_name)
        
        logger.info(f"Evaluation on {dataset_name} completed!")
        return eval_result
    
    def _generate_classification_report(self, predictions, dataset_name: str):
        """Generate detailed classification report."""
        pred_outputs, labels = predictions.predictions, predictions.label_ids
        
        # Handle multi-task outputs
        if isinstance(pred_outputs, tuple):
            risk_preds = pred_outputs[0]
        else:
            risk_preds = pred_outputs
        
        if isinstance(labels, tuple):
            risk_labels = labels[0]
        else:
            risk_labels = labels
        
        # Get predicted labels
        risk_pred_labels = np.argmax(risk_preds, axis=1)
        
        # Generate classification report
        report = classification_report(
            risk_labels, 
            risk_pred_labels, 
            output_dict=True,
            zero_division=0
        )
        
        # Save report
        with open(self.output_dir / f"{dataset_name}_classification_report.json", 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        logger.info(f"Classification Report ({dataset_name}):")
        logger.info(f"Macro F1: {report['macro avg']['f1-score']:.4f}")
        logger.info(f"Weighted F1: {report['weighted avg']['f1-score']:.4f}")
        logger.info(f"Accuracy: {report['accuracy']:.4f}")
    
    def predict(self, texts: List[str]) -> List[Dict]:
        """Make predictions on new texts."""
        self.model.eval()
        predictions = []
        
        for text in texts:
            pred = self.model.predict_risk(text)
            predictions.append(pred)
        
        return predictions
    
    def save_model(self, path: Optional[str] = None):
        """Save the trained model."""
        if path is None:
            path = self.output_dir / "final_model"
        
        self.trainer.save_model(str(path))
        
        # Save config
        config_dict = self.config.__dict__
        with open(Path(path) / "config.json", 'w') as f:
            json.dump(config_dict, f, indent=2)
        
        logger.info(f"Model saved to {path}")


class MultiTaskLoss(nn.Module):
    """Multi-task loss for risk classification."""
    
    def __init__(
        self,
        task_weights: Optional[Dict[str, float]] = None,
        use_uncertainty_weighting: bool = False
    ):
        """
        Initialize multi-task loss.
        
        Args:
            task_weights: Manual weights for each task
            use_uncertainty_weighting: Use uncertainty-based weighting
        """
        super().__init__()
        self.task_weights = task_weights or {
            'risk_category': 1.0,
            'severity': 0.5,
            'binary_risk': 0.3,
            'span': 0.8
        }
        self.use_uncertainty_weighting = use_uncertainty_weighting
        
        if use_uncertainty_weighting:
            # Learnable uncertainty parameters
            self.log_vars = nn.Parameter(torch.zeros(len(self.task_weights)))
    
    def forward(self, losses: Dict[str, torch.Tensor]) -> torch.Tensor:
        """Compute weighted multi-task loss."""
        if self.use_uncertainty_weighting:
            # Uncertainty-based weighting
            total_loss = 0
            for i, (task, loss) in enumerate(losses.items()):
                if task in self.task_weights:
                    precision = torch.exp(-self.log_vars[i])
                    total_loss += precision * loss + self.log_vars[i]
        else:
            # Manual weighting
            total_loss = 0
            for task, loss in losses.items():
                weight = self.task_weights.get(task, 1.0)
                total_loss += weight * loss
        
        return total_loss


def create_trainer(
    model_config: ModelConfig,
    data_module: RiskDataModule,
    output_dir: str = "outputs/",
    use_wandb: bool = False
) -> RiskClassificationTrainer:
    """Factory function to create trainer."""
    from src.models.risk_classifier import create_model
    
    model = create_model(model_config)
    
    return RiskClassificationTrainer(
        model=model,
        data_module=data_module,
        config=model_config,
        output_dir=output_dir,
        use_wandb=use_wandb
    )
