{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CUAD Dataset Exploration\n", "\n", "This notebook provides comprehensive exploration of the CUAD v1 dataset for risk clause detection.\n", "\n", "## Objectives\n", "1. Load and examine CUAD dataset structure\n", "2. Analyze clause type distributions\n", "3. Identify risk-related clause types\n", "4. Examine document and span characteristics\n", "5. Prepare data for risk taxonomy mapping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup\n", "import sys\n", "sys.path.append('..')\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import json\n", "\n", "# Custom modules\n", "from src.data.cuad_loader import CUADLoader\n", "from src.data.data_analyzer import CUADAnalyzer\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Dataset Loading and Basic Statistics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize loader and analyzer\n", "loader = CUADLoader(\"../data/raw/\")\n", "analyzer = CUADAnalyzer(\"../data/raw/\")\n", "\n", "# Load basic statistics\n", "try:\n", "    stats = loader.get_dataset_statistics()\n", "    print(\"✅ CUAD dataset loaded successfully!\")\n", "    print(f\"Documents: {stats['total_documents']}\")\n", "    print(f\"Clauses: {stats['total_clauses']}\")\n", "    print(f\"Clause types: {stats['unique_clause_types']}\")\nexcept Exception as e:\n", "    print(f\"❌ Error loading dataset: {e}\")\n", "    print(\"Please ensure CUAD v1 is downloaded to ../data/raw/\")\n", "    print(\"Run: python ../scripts/setup_data.py\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Comprehensive Dataset Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive analysis report\n", "if 'stats' in locals():\n", "    report = analyzer.generate_summary_report()\n", "    print(report)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Clause Type Distribution Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze clause distribution\n", "if 'stats' in locals():\n", "    clause_dist = analyzer.analyze_clause_distribution()\n", "    \n", "    print(\"Top 15 Clause Types:\")\n", "    print(clause_dist.head(15).to_string(index=False))\n", "    \n", "    # Plot distribution\n", "    fig = analyzer.plot_clause_distribution(top_n=20)\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Risk-Related Clause Identification"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identify risk-related clauses\n", "if 'stats' in locals():\n", "    risk_clauses = analyzer.identify_risk_related_clauses()\n", "    \n", "    print(f\"Found {len(risk_clauses)} risk-related clause types:\")\n", "    print(\"\\nRisk-Related Clauses with Counts:\")\n", "    \n", "    risk_data = []\n", "    for clause in risk_clauses:\n", "        count = stats['clause_type_distribution'].get(clause, 0)\n", "        risk_data.append({'clause_type': clause, 'count': count})\n", "    \n", "    risk_df = pd.DataFrame(risk_data).sort_values('count', ascending=False)\n", "    print(risk_df.to_string(index=False))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Document Length Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze document characteristics\n", "if 'stats' in locals():\n", "    doc_analysis = analyzer.analyze_document_lengths()\n", "    \n", "    print(\"Document Length Statistics:\")\n", "    length_stats = doc_analysis['text_length_stats']\n", "    for key, value in length_stats.items():\n", "        print(f\"  {key}: {value:,.0f} characters\")\n", "    \n", "    # Plot document lengths\n", "    fig = analyzer.plot_document_lengths()\n", "    plt.show()\n", "    \n", "    # Check for long documents (>4k tokens ≈ 16k characters)\n", "    long_docs = [l for l in doc_analysis['raw_lengths'] if l > 16000]\n", "    print(f\"\\nDocuments >16k characters (≈4k tokens): {len(long_docs)} ({len(long_docs)/len(doc_analysis['raw_lengths'])*100:.1f}%)\")\n", "    print(f\"Documents >64k characters (≈16k tokens): {len([l for l in doc_analysis['raw_lengths'] if l > 64000])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Span Characteristics Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze span characteristics\n", "if 'stats' in locals():\n", "    span_analysis = analyzer.analyze_span_characteristics()\n", "    \n", "    print(\"Clause Span Statistics:\")\n", "    span_stats = span_analysis['span_length_stats']\n", "    for key, value in span_stats.items():\n", "        print(f\"  {key}: {value:.0f} characters\")\n", "    \n", "    # Plot span length distribution\n", "    plt.figure(figsize=(12, 4))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    plt.hist(span_analysis['raw_span_lengths'], bins=50, alpha=0.7)\n", "    plt.xlabel('Span Length (characters)')\n", "    plt.ylabel('Frequency')\n", "    plt.title('Clause Span Length Distribution')\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    plt.hist(span_analysis['raw_span_positions'], bins=30, alpha=0.7)\n", "    plt.xlabel('Relative Position in Document')\n", "    plt.ylabel('Frequency')\n", "    plt.title('Clause Position Distribution')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Contract Type Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze contract types\n", "if 'stats' in locals():\n", "    contract_types = analyzer.analyze_contract_types()\n", "    \n", "    print(\"Contract Type Distribution:\")\n", "    contract_df = pd.DataFrame([\n", "        {'type': k, 'count': v, 'percentage': v/sum(contract_types.values())*100}\n", "        for k, v in contract_types.items()\n", "    ]).sort_values('count', ascending=False)\n", "    \n", "    print(contract_df.to_string(index=False))\n", "    \n", "    # Plot contract types\n", "    plt.figure(figsize=(10, 6))\n", "    plt.pie(contract_df['count'], labels=contract_df['type'], autopct='%1.1f%%')\n", "    plt.title('Contract Type Distribution')\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Sample Data Examination"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and examine sample documents\n", "if 'stats' in locals():\n", "    processed_docs = analyzer.load_data()\n", "    \n", "    print(\"Sample Document Analysis:\")\n", "    print(f\"First document: {processed_docs[0]['title']}\")\n", "    print(f\"Text length: {processed_docs[0]['text_length']:,} characters\")\n", "    print(f\"Number of clauses: {processed_docs[0]['num_clauses']}\")\n", "    \n", "    if processed_docs[0]['clauses']:\n", "        print(\"\\nSample clauses:\")\n", "        for i, clause in enumerate(processed_docs[0]['clauses'][:3]):\n", "            print(f\"\\nClause {i+1}:\")\n", "            print(f\"  Type: {clause['clause_type']}\")\n", "            print(f\"  Text: {clause['span_text'][:100]}...\")\n", "            print(f\"  Length: {len(clause['span_text'])} characters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Export Analysis Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export analysis results for further processing\n", "if 'stats' in locals():\n", "    analyzer.export_analysis_results(\"../data/processed/\")\n", "    print(\"✅ Analysis results exported to ../data/processed/\")\n", "    \n", "    # Show exported files\n", "    processed_dir = Path(\"../data/processed/\")\n", "    if processed_dir.exists():\n", "        files = list(processed_dir.glob(\"*\"))\n", "        print(\"\\nExported files:\")\n", "        for file in files:\n", "            print(f\"  {file.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This exploration provides the foundation for:\n", "1. **Risk Taxonomy Mapping**: Use identified risk-related clauses\n", "2. **Model Selection**: Consider document lengths for transformer choice\n", "3. **Training Strategy**: Account for class imbalance in clause types\n", "4. **Evaluation Design**: Focus on span-level metrics\n", "\n", "Next steps:\n", "- Run `02_risk_mapping.ipynb` to create risk taxonomy\n", "- Design training pipeline for long-document models\n", "- Implement evaluation metrics for imbalanced span extraction"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}