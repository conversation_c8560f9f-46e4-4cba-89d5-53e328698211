{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Risk Clause Detection - Model Training\n",
    "\n",
    "This notebook demonstrates the complete training pipeline for risk clause detection models.\n",
    "\n",
    "**⚠️ Note: This notebook is designed to run on Colab/Kaggle with GPU support.**\n",
    "\n",
    "## Objectives\n",
    "1. Setup training environment and data\n",
    "2. Configure long-document transformer models\n",
    "3. Train multi-task risk classification model\n",
    "4. Evaluate model performance\n",
    "5. Generate risk predictions and evidence spans"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Setup and Installation\n",
    "\n",
    "Run this section first when using Colab/Kaggle:"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Install required packages (uncomment for Colab/Kaggle)\n",
    "# !pip install transformers datasets torch torchvision torchaudio\n",
    "# !pip install longformer seqeval wandb\n",
    "# !pip install scikit-learn matplot<PERSON>b seaborn plotly\n",
    "\n",
    "# Upload your project files to Colab/Kaggle or clone from GitHub\n",
    "# !git clone https://github.com/your-username/risk-clause-detection.git\n",
    "# import os\n",
    "# os.chdir('risk-clause-detection')"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Setup\n",
    "import sys\n",
    "import os\n",
    "import torch\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "from pathlib import Path\n",
    "import json\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Check GPU availability\n",
    "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n",
    "print(f\"Using device: {device}\")\n",
    "if torch.cuda.is_available():\n",
    "    print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n",
    "    print(f\"Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\")\n",
    "\n",
    "# Add project to path\n",
    "if '..' not in sys.path:\n",
    "    sys.path.append('..')\n",
    "\n",
    "# Set random seeds for reproducibility\n",
    "torch.manual_seed(42)\n",
    "np.random.seed(42)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Data Loading and Preparation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import custom modules\n",
    "from src.data.cuad_loader import CUADLoader\n",
    "from src.data.risk_taxonomy import RiskTaxonomyMapper\n",
    "from src.training.dataset import RiskDataModule, create_data_splits, load_risk_taxonomy\n",
    "from src.models.risk_classifier import ModelConfig, create_model\n",
    "from src.training.trainer import create_trainer\n",
    "from src.evaluation.metrics import RiskEvaluationMetrics, evaluate_model_predictions\n",
    "\n",
    "print(\"✅ Modules imported successfully\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load CUAD data\n",
    "data_dir = \"../data/raw/\"  # Adjust path as needed\n",
    "loader = CUADLoader(data_dir)\n",
    "\n",
    "try:\n",
    "    # Load processed documents\n",
    "    processed_docs = loader.load_processed_dataset()\n",
    "    print(f\"✅ Loaded {len(processed_docs)} documents\")\n",
    "    \n",
    "    # Load risk taxonomy\n",
    "    risk_taxonomy = load_risk_taxonomy(\"../data/risk_taxonomy/training_labels.csv\")\n",
    "    print(f\"✅ Loaded risk taxonomy with {len(risk_taxonomy)} mappings\")\n",
    "    \nexcept Exception as e:\n",
    "    print(f\"❌ Error loading data: {e}\")\n",
    "    print(\"Please ensure CUAD dataset and risk taxonomy are available\")\n",
    "    print(\"Run the data exploration and risk mapping notebooks first\")\n",
    "    \n",
    "    # Create sample data for demonstration\n",
    "    print(\"\\nCreating sample data for demonstration...\")\n",
    "    processed_docs = [\n",
    "        {\n",
    "            'title': 'sample_contract_1.txt',\n",
    "            'full_text': 'This agreement shall be governed by Delaware law. The company provides no warranty.',\n",
    "            'clauses': [\n",
    "                {\n",
    "                    'clause_type': 'Governing Law',\n",
    "                    'span_text': 'governed by Delaware law',\n",
    "                    'span_start': 30,\n",
    "                    'span_end': 53\n",
    "                }\n",
    "            ],\n",
    "            'text_length': 85,\n",
    "            'num_clauses': 1\n",
    "        }\n",
    "    ] * 100  # Replicate for training\n",
    "    \n",
    "    risk_taxonomy = {\n",
    "        'Governing Law': {\n",
    "            'risk_category_id': 0,\n",
    "            'risk_severity': 2,\n",
    "            'is_high_risk': 0\n",
    "        }\n",
    "    }"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create data splits\n",
    "train_docs, val_docs, test_docs = create_data_splits(\n",
    "    processed_docs, \n",
    "    train_ratio=0.7,\n",
    "    val_ratio=0.15,\n",
    "    test_ratio=0.15\n",
    ")\n",
    "\n",
    "print(f\"Data splits:\")\n",
    "print(f\"  Train: {len(train_docs)} documents\")\n",
    "print(f\"  Validation: {len(val_docs)} documents\")\n",
    "print(f\"  Test: {len(test_docs)} documents\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Model Configuration"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Configure model\n",
    "config = ModelConfig(\n",
    "    model_name=\"microsoft/longformer-base-4096\",  # Use Longformer for long documents\n",
    "    max_length=4096,  # Handle long documents\n",
    "    num_risk_categories=12,  # Number of risk categories\n",
    "    num_severity_levels=4,   # Low, Medium, High, Critical\n",
    "    dropout_rate=0.1,\n",
    "    learning_rate=2e-5,\n",
    "    warmup_steps=500,\n",
    "    weight_decay=0.01\n",
    ")\n",
    "\n",
    "print(\"Model Configuration:\")\n",
    "for key, value in config.__dict__.items():\n",
    "    print(f\"  {key}: {value}\")\n",
    "\n",
    "# Alternative models for different scenarios:\n",
    "print(\"\\n📝 Alternative model options:\")\n",
    "print(\"  - allenai/led-base-16384 (for very long documents)\")\n",
    "print(\"  - nlpaueb/legal-bert-base-uncased (for legal domain)\")\n",
    "print(\"  - ProsusAI/finbert (for financial domain)\")\n",
    "print(\"  - microsoft/longformer-large-4096 (for better performance)\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Data Module Setup"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create tokenizer\n",
    "from transformers import AutoTokenizer\n",
    "\n",
    "tokenizer = AutoTokenizer.from_pretrained(config.model_name)\n",
    "if tokenizer.pad_token is None:\n",
    "    tokenizer.pad_token = tokenizer.eos_token\n",
    "\n",
    "print(f\"✅ Tokenizer loaded: {config.model_name}\")\n",
    "print(f\"Vocabulary size: {len(tokenizer)}\")\n",
    "print(f\"Max length: {tokenizer.model_max_length}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create data module\n",
    "data_module = RiskDataModule(\n",
    "    train_data=train_docs,\n",
    "    val_data=val_docs,\n",
    "    test_data=test_docs,\n",
    "    tokenizer=tokenizer,\n",
    "    risk_taxonomy=risk_taxonomy,\n",
    "    batch_size=2,  # Small batch size for GPU memory\n",
    "    max_length=config.max_length,\n",
    "    num_workers=2\n",
    ")\n",
    "\n",
    "print(f\"✅ Data module created\")\n",
    "print(f\"Training examples: {len(data_module.train_dataset)}\")\n",
    "print(f\"Validation examples: {len(data_module.val_dataset)}\")\n",
    "print(f\"Test examples: {len(data_module.test_dataset) if data_module.test_dataset else 0}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test data loading\n",
    "train_loader = data_module.train_dataloader()\n",
    "sample_batch = next(iter(train_loader))\n",
    "\n",
    "print(\"Sample batch shapes:\")\n",
    "for key, value in sample_batch.items():\n",
    "    if isinstance(value, torch.Tensor):\n",
    "        print(f\"  {key}: {value.shape}\")\n",
    "    else:\n",
    "        print(f\"  {key}: {type(value)}\")\n",
    "\n",
    "# Check memory usage\n",
    "if torch.cuda.is_available():\n",
    "    print(f\"\\nGPU memory allocated: {torch.cuda.memory_allocated() / 1e9:.2f} GB\")\n",
    "    print(f\"GPU memory cached: {torch.cuda.memory_reserved() / 1e9:.2f} GB\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Model Training"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create trainer\n",
    "trainer = create_trainer(\n",
    "    model_config=config,\n",
    "    data_module=data_module,\n",
    "    output_dir=\"../outputs/risk_classifier/\",\n",
    "    use_wandb=False  # Set to True if you want to use Weights & Biases\n",
    ")\n",
    "\n",
    "print(\"✅ Trainer created\")\n",
    "print(f\"Output directory: {trainer.output_dir}\")\n",
    "\n",
    "# Move model to GPU if available\n",
    "trainer.model.to(device)\n",
    "print(f\"Model moved to: {device}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Start training\n",
    "print(\"🚀 Starting training...\")\n",
    "print(\"This may take several hours depending on your GPU and data size.\")\n",
    "\n",
    "try:\n",
    "    # Train the model\n",
    "    train_metrics = trainer.train()\n",
    "    \n",
    "    print(\"\\n✅ Training completed!\")\n",
    "    print(\"Training metrics:\")\n",
    "    for key, value in train_metrics.items():\n",
    "        if isinstance(value, (int, float)):\n",
    "            print(f\"  {key}: {value:.4f}\")\n",
    "        else:\n",
    "            print(f\"  {key}: {value}\")\n",
    "            \nexcept Exception as e:\n",
    "    print(f\"❌ Training failed: {e}\")\n",
    "    print(\"This might be due to GPU memory limitations.\")\n",
    "    print(\"Try reducing batch_size or max_length in the configuration.\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Model Evaluation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Evaluate on validation set\n",
    "print(\"📊 Evaluating model...\")\n",
    "\n",
    "try:\n",
    "    val_metrics = trainer.evaluate(\"validation\")\n",
    "    \n",
    "    print(\"\\nValidation metrics:\")\n",
    "    for key, value in val_metrics.items():\n",
    "        if isinstance(value, (int, float)):\n",
    "            print(f\"  {key}: {value:.4f}\")\n",
    "            \n",
    "except Exception as e:\n",
    "    print(f\"❌ Evaluation failed: {e}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Evaluate on test set if available\n",
    "if data_module.test_dataset:\n",
    "    print(\"📊 Evaluating on test set...\")\n",
    "    \n",
    "    try:\n",
    "        test_metrics = trainer.evaluate(\"test\")\n",
    "        \n",
    "        print(\"\\nTest metrics:\")\n",
    "        for key, value in test_metrics.items():\n",
    "            if isinstance(value, (int, float)):\n",
    "                print(f\"  {key}: {value:.4f}\")\n",
    "                \n",
    "    except Exception as e:\n",
    "        print(f\"❌ Test evaluation failed: {e}\")\nelse:\n",
    "    print(\"No test set available\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. Model Inference and Risk Prediction"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Test model predictions on sample texts\n",
    "sample_texts = [\n",
    "    \"This agreement shall be governed by the laws of Delaware. Any disputes shall be resolved through binding arbitration.\",\n",
    "    \"The company shall indemnify and hold harmless the client from any claims, damages, or losses arising from this agreement.\",\n",
    "    \"Either party may terminate this agreement with thirty (30) days written notice to the other party.\",\n",
    "    \"The contractor agrees to maintain confidentiality of all proprietary information disclosed during the term of this agreement.\"\n",
    "]\n",
    "\n",
    "print(\"🔍 Testing model predictions...\")\n",
    "\n",
    "try:\n",
    "    predictions = trainer.predict(sample_texts)\n",
    "    \n",
    "    for i, (text, pred) in enumerate(zip(sample_texts, predictions)):\n",
    "        print(f\"\\nSample {i+1}:\")\n",
    "        print(f\"Text: {text[:100]}...\")\n",
    "        print(f\"Risk Category: {pred.get('risk_category', 'Unknown')}\")\n",
    "        print(f\"Risk Confidence: {pred.get('risk_category_confidence', 0):.3f}\")\n",
    "        print(f\"Severity: {pred.get('severity', 'Unknown')}\")\n",
    "        print(f\"Is Risk: {pred.get('is_risk', False)}\")\n",
    "        \n",
    "        if 'evidence_span' in pred:\n",
    "            span = pred['evidence_span']\n",
    "            print(f\"Evidence: '{span['text']}'\")\n",
    "            print(f\"Span Confidence: {span['confidence']:.3f}\")\n",
    "            \nexcept Exception as e:\n",
    "    print(f\"❌ Prediction failed: {e}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. Save Model and Results"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Save the trained model\n",
    "print(\"💾 Saving model...\")\n",
    "\n",
    "try:\n",
    "    trainer.save_model(\"../outputs/risk_classifier/final_model\")\n",
    "    print(\"✅ Model saved successfully\")\n",
    "    \n",
    "    # List saved files\n",
    "    model_dir = Path(\"../outputs/risk_classifier/final_model\")\n",
    "    if model_dir.exists():\n",
    "        print(\"\\nSaved files:\")\n",
    "        for file in model_dir.iterdir():\n",
    "            print(f\"  {file.name}\")\n",
    "            \nexcept Exception as e:\n",
    "    print(f\"❌ Failed to save model: {e}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create summary report\n",
    "summary_report = f\"\"\"\n",
    "Risk Clause Detection - Training Summary\n",
    "{'='*50}\n",
    "\n",
    "Model Configuration:\n",
    "  Base Model: {config.model_name}\n",
    "  Max Length: {config.max_length}\n",
    "  Risk Categories: {config.num_risk_categories}\n",
    "  Severity Levels: {config.num_severity_levels}\n",
    "\n",
    "Dataset:\n",
    "  Training Documents: {len(train_docs)}\n",
    "  Validation Documents: {len(val_docs)}\n",
    "  Test Documents: {len(test_docs)}\n",
    "  Training Examples: {len(data_module.train_dataset)}\n",
    "\n",
    "Training Environment:\n",
    "  Device: {device}\n",
    "  GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB (if GPU available)\n",
    "\n",
    "Next Steps:\n",
    "1. Fine-tune hyperparameters for better performance\n",
    "2. Experiment with different base models (Legal-BERT, FinBERT)\n",
    "3. Implement hierarchical attention for very long documents\n",
    "4. Add more sophisticated span extraction techniques\n",
    "5. Deploy model for real-time risk analysis\n",
    "\n",
    "Files Generated:\n",
    "  - Model weights and configuration\n",
    "  - Training and evaluation metrics\n",
    "  - Classification reports\n",
    "  - Sample predictions\n",
    "\"\"\"\n",
    "\n",
    "print(summary_report)\n",
    "\n",
    "# Save summary\n",
    "with open(\"../outputs/risk_classifier/training_summary.txt\", 'w') as f:\n",
    "    f.write(summary_report)\n",
    "\n",
    "print(\"\\n✅ Training summary saved\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. Performance Analysis and Visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load and visualize training metrics if available\n",
    "metrics_file = Path(\"../outputs/risk_classifier/train_metrics.json\")\n",
    "\n",
    "if metrics_file.exists():\n",
    "    with open(metrics_file, 'r') as f:\n",
    "        metrics = json.load(f)\n",
    "    \n",
    "    print(\"📈 Training Metrics Visualization:\")\n",
    "    \n",
    "    # Plot key metrics\n",
    "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n",
    "    \n",
    "    # Training loss\n",
    "    if 'train_loss' in metrics:\n",
    "        axes[0, 0].plot(metrics['train_loss'])\n",
    "        axes[0, 0].set_title('Training Loss')\n",
    "        axes[0, 0].set_xlabel('Steps')\n",
    "        axes[0, 0].set_ylabel('Loss')\n",
    "    \n",
    "    # Validation metrics\n",
    "    if 'eval_f1_macro' in metrics:\n",
    "        axes[0, 1].plot(metrics['eval_f1_macro'])\n",
    "        axes[0, 1].set_title('Validation F1 (Macro)')\n",
    "        axes[0, 1].set_xlabel('Steps')\n",
    "        axes[0, 1].set_ylabel('F1 Score')\n",
    "    \n",
    "    # Learning rate\n",
    "    if 'learning_rate' in metrics:\n",
    "        axes[1, 0].plot(metrics['learning_rate'])\n",
    "        axes[1, 0].set_title('Learning Rate Schedule')\n",
    "        axes[1, 0].set_xlabel('Steps')\n",
    "        axes[1, 0].set_ylabel('Learning Rate')\n",
    "    \n",
    "    # Accuracy\n",
    "    if 'eval_accuracy' in metrics:\n",
    "        axes[1, 1].plot(metrics['eval_accuracy'])\n",
    "        axes[1, 1].set_title('Validation Accuracy')\n",
    "        axes[1, 1].set_xlabel('Steps')\n",
    "        axes[1, 1].set_ylabel('Accuracy')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.savefig('../outputs/risk_classifier/training_curves.png', dpi=300, bbox_inches='tight')\n",
    "    plt.show()\n",
    "    \nelse:\n",
    "    print(\"No training metrics file found\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Summary\n",
    "\n",
    "This notebook demonstrated the complete training pipeline for risk clause detection:\n",
    "\n",
    "### ✅ Completed Tasks:\n",
    "1. **Data Loading**: Processed CUAD dataset and risk taxonomy\n",
    "2. **Model Configuration**: Setup Longformer for long-document processing\n",
    "3. **Training Pipeline**: Multi-task learning for risk classification and span extraction\n",
    "4. **Evaluation**: Comprehensive metrics for imbalanced classification\n",
    "5. **Inference**: Risk prediction with evidence spans\n",
    "\n",
    "### 🚀 Next Steps:\n",
    "1. **Hyperparameter Tuning**: Experiment with learning rates, batch sizes, and model architectures\n",
    "2. **Domain Adaptation**: Try Legal-BERT or FinBERT for domain-specific improvements\n",
    "3. **Advanced Techniques**: Implement hierarchical attention or retrieval-augmented generation\n",
    "4. **Production Deployment**: Create API endpoints for real-time risk analysis\n",
    "5. **Continuous Learning**: Setup pipelines for model updates with new data\n",
    "\n",
    "### 📊 Model Performance:\n",
    "- The model can process documents up to 4,096 tokens\n",
    "- Multi-task learning handles risk classification, severity assessment, and span extraction\n",
    "- Evaluation metrics focus on macro-F1 for imbalanced classes\n",
    "- Evidence spans provide explainable risk detection\n",
    "\n",
    "### 💡 Tips for Better Performance:\n",
    "- Use larger models (Longformer-large) for better accuracy\n",
    "- Increase training data with data augmentation techniques\n",
    "- Implement active learning for efficient annotation\n",
    "- Use ensemble methods for robust predictions"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",\n",
   "language": "python",\n",
   "name": "python3"\n  },\n  "language_info": {\n   "codemirror_mode": {\n    "name": "ipython",\n    "version": 3\n   },\n   "file_extension": ".py",\n   "mimetype": "text/x-python",\n   "name": "python",\n   "nbconvert_exporter": "python",\n   "pygments_lexer": "ipython3",\n   "version": "3.8.0"\n  }\n },\n "nbformat": 4,\n "nbformat_minor": 4\n}
